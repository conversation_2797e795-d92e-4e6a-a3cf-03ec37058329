const {
  getAllIncomeSubcategories,
  addIncomeSubcategory,
  editIncomeSubcategory,
  deleteIncomeSubcategory,
  seedDefaultIncomeSubcategories,
} = require("../controllers/IncomeSubcategoryController");

const Router = require("express").Router;

const incomeSubcategoryRouter = Router();

// Get all income subcategories
incomeSubcategoryRouter.get("/income-subcategories", getAllIncomeSubcategories);

// Add a new income subcategory
incomeSubcategoryRouter.post("/income-subcategories", addIncomeSubcategory);

// Edit an existing income subcategory
incomeSubcategoryRouter.put("/income-subcategories/:id", editIncomeSubcategory);

// Delete an income subcategory
incomeSubcategoryRouter.delete("/income-subcategories/:id", deleteIncomeSubcategory);

// Seed default income subcategories
incomeSubcategoryRouter.post("/income-subcategories/seed", seedDefaultIncomeSubcategories);

module.exports = incomeSubcategoryRouter;