import React from 'react';
import {
  Box,
  Chip,
  Typography,
  Button,
  Tooltip,
  IconButton
} from '@mui/material';
import {
  LocationOn as LocationIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useRegion } from '../../context/RegionContext';

const ActiveRegionDisplay = ({ variant = 'default', showButton = true }) => {
  const navigate = useNavigate();
  const { activeRegion, clearActiveRegion } = useRegion();

  const handleChangeRegion = () => {
    navigate('/region-selection');
  };

  if (!activeRegion) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Chip
          icon={<LocationIcon />}
          label="No Region Selected"
          color="warning"
          size="small"
          sx={{ fontWeight: 500 }}
        />
        {showButton && (
          <Button
            size="small"
            variant="outlined"
            onClick={handleChangeRegion}
            sx={{
              color: '#375e38',
              borderColor: '#375e38',
              '&:hover': {
                backgroundColor: 'rgba(55, 94, 56, 0.1)',
                borderColor: '#375e38'
              }
            }}
          >
            Select Region
          </Button>
        )}
      </Box>
    );
  }

  // Compact variant (for headers, etc.)
  if (variant === 'compact') {
    return (
      <Tooltip title="Click to change region">
        <Chip
          icon={<LocationIcon />}
          label={activeRegion.name || activeRegion.regionName}
          color="primary"
          size="small"
          onClick={handleChangeRegion}
          sx={{ 
            fontWeight: 600,
            backgroundColor: '#375e38',
            '&:hover': {
              backgroundColor: '#2e4e2e'
            }
          }}
        />
      </Tooltip>
    );
  }

  // Default variant with more information
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Chip
        icon={<LocationIcon />}
        label={`Region: ${activeRegion.name || activeRegion.regionName}`}
        color="primary"
        size="small"
        sx={{ 
          fontWeight: 600,
          backgroundColor: '#375e38',
          '&:hover': {
            backgroundColor: '#2e4e2e'
          }
        }}
      />
      {showButton && (
        <Tooltip title="Change region">
          <IconButton
            size="small"
            onClick={handleChangeRegion}
            sx={{
              color: '#375e38',
              '&:hover': {
                backgroundColor: 'rgba(55, 94, 56, 0.1)'
              }
            }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      )}
    </Box>
  );
};

export default ActiveRegionDisplay;