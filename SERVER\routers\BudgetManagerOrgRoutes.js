const {
  getAvailableOrganizationalUnits,
  validateOrganizationalSelection,
  getOrganizationalStats,
  testOrganizationalSegregation
} = require('../controllers/BudgetManagerOrgController');

const Router = require('express').Router;
const { 
  authenticatedRoute, 
  adminRoute,
  PERMISSION_LEVELS 
} = require('../middleware/securityMiddleware');

const { 
  budgetManagerOrgSelection,
  getAvailableOrgUnits,
  logOrgSelection,
  validateOrgSelection
} = require('../middleware/budgetManagerOrgSelection');

const budgetManagerOrgRouter = Router();

// 🔒 SECURED ROUTES FOR BUDGET MANAGER ORGANIZATIONAL SELECTION

// Get all available organizational units for Budget Manager selection
budgetManagerOrgRouter.get(
  '/budget-manager/organizational-units',
  ...adminRoute(),
  getAvailableOrganizationalUnits
);

// Validate organizational unit selection
budgetManagerOrgRouter.post(
  '/budget-manager/validate-org-selection',
  ...adminRoute(),
  validateOrganizationalSelection
);

// Get organizational statistics for Budget Manager dashboard
budgetManagerOrgRouter.get(
  '/budget-manager/organizational-stats',
  ...adminRoute(),
  getOrganizationalStats
);

// Test organizational segregation
budgetManagerOrgRouter.get(
  '/budget-manager/test-segregation',
  ...adminRoute(),
  testOrganizationalSegregation
);

// Budget Manager creating REAL proposal with organizational selection
budgetManagerOrgRouter.post(
  '/budget-manager/create-proposal',
  ...adminRoute(),
  budgetManagerOrgSelection(),
  validateOrgSelection(['region']), // Require region selection
  logOrgSelection(),
  async (req, res) => {
    try {
      const {
        region,
        department,
        division,
        proposalType,
        proposalData
      } = req.body;
      const budgetManagerContext = req.budgetManagerContext;

      if (!proposalType) {
        return res.status(400).json({
          message: 'proposalType is required (personnel, mooe, capitalOutlay, cos)'
        });
      }

      let createdProposal;

      // Create REAL proposal in database based on type
      switch (proposalType) {
        case 'personnel':
          const PersonnelServices = require('../models/PersonnelServices');
          createdProposal = await PersonnelServices.create({
            ...proposalData,
            region: region,
            department: department,
            division: division,
            processBy: req.user.id,
            processDate: new Date(),
            fiscalYear: proposalData.fiscalYear || '2024',
            status: 'Draft'
          });
          break;

        case 'mooe':
          const MooeProposal = require('../models/mooeProposals');
          createdProposal = await MooeProposal.create({
            ...proposalData,
            region: region,
            processBy: req.user.id,
            processDate: new Date(),
            fiscalYear: proposalData.fiscalYear || '2024',
            status: 'Draft'
          });
          break;

        case 'capitalOutlay':
          const CapitalOutlay = require('../models/CapitalOutlay');
          createdProposal = await CapitalOutlay.create({
            ...proposalData,
            region: region,
            department: department,
            processBy: req.user.id,
            processDate: new Date(),
            fiscalYear: proposalData.fiscalYear || '2024',
            status: 'Not Submitted'
          });
          break;

        case 'cos':
          const COSPersonnel = require('../models/COSPersonnel');
          createdProposal = await COSPersonnel.create({
            ...proposalData,
            region: region,
            department: department,
            division: division,
            processBy: req.user.id,
            processDate: new Date(),
            fiscalYear: proposalData.fiscalYear || '2024'
          });
          break;

        default:
          return res.status(400).json({
            message: 'Invalid proposalType. Use: personnel, mooe, capitalOutlay, or cos'
          });
      }

      res.status(201).json({
        message: 'REAL proposal created successfully in database with organizational context',
        dataSource: 'MongoDB Database',
        proposal: {
          id: createdProposal._id,
          type: proposalType,
          ...createdProposal.toObject(),
          budgetManagerContext: {
            selectedBy: budgetManagerContext.selectedBy,
            selectedByRole: budgetManagerContext.selectedByRole,
            canSelectAnyOrg: budgetManagerContext.canSelectAnyOrg
          }
        },
        segregationInfo: {
          message: 'Data is segregated by organizational unit in database',
          selectedOrganization: {
            region: region,
            department: department,
            division: division
          },
          budgetManagerPrivileges: {
            canSelectAnyOrg: true,
            canSwitchOrganizations: true,
            hasFullAccess: true,
            createdRealDatabaseRecord: true
          },
          verification: {
            canVerifySegregation: `Query database with filters: region='${region}'${department ? `, department='${department}'` : ''}${division ? `, division='${division}'` : ''}`
          }
        }
      });
    } catch (error) {
      console.error('Error creating proposal:', error);
      res.status(500).json({ message: 'Server error', error: error.message });
    }
  }
);

// Example: Budget Manager viewing REAL proposals with organizational filtering
budgetManagerOrgRouter.get(
  '/budget-manager/view-proposals',
  ...adminRoute(),
  budgetManagerOrgSelection(),
  logOrgSelection(),
  async (req, res) => {
    try {
      const { region, department, division, category } = req.query;
      const budgetManagerContext = req.budgetManagerContext;

      // Import real models
      const PersonnelServices = require('../models/PersonnelServices');
      const MooeProposal = require('../models/mooeProposals');
      const CapitalOutlay = require('../models/CapitalOutlay');
      const COSPersonnel = require('../models/COSPersonnel');

      // Build filters based on organizational selection
      const filters = {};
      if (region) filters.region = region;
      if (department) filters.department = department;
      if (division) filters.division = division;

      // Get REAL proposal data from database
      let proposals = [];
      let totalCount = 0;

      if (!category || category === 'all') {
        // Get all types of proposals
        const [personnel, mooe, capitalOutlay, cosPersonnel] = await Promise.all([
          PersonnelServices.find(filters)
            .select('employeeFullName region department division fiscalYear Total status processBy processDate')
            .sort({ processDate: -1 })
            .limit(20)
            .lean(),
          MooeProposal.find(filters)
            .select('accountingTitle region amount fiscalYear status processBy processDate')
            .sort({ processDate: -1 })
            .limit(20)
            .lean(),
          CapitalOutlay.find(filters)
            .select('particulars region department cost fiscalYear status processBy processDate')
            .sort({ processDate: -1 })
            .limit(20)
            .lean(),
          COSPersonnel.find(filters)
            .select('employeeFullName region department division fiscalYear Total processBy processDate')
            .sort({ processDate: -1 })
            .limit(20)
            .lean()
        ]);

        // Format and combine all proposals
        proposals = [
          ...personnel.map(p => ({
            id: p._id,
            type: 'Personnel Services',
            title: p.employeeFullName,
            region: p.region,
            department: p.department,
            division: p.division,
            amount: p.Total,
            fiscalYear: p.fiscalYear,
            status: p.status,
            processBy: p.processBy,
            processDate: p.processDate
          })),
          ...mooe.map(m => ({
            id: m._id,
            type: 'MOOE',
            title: m.accountingTitle,
            region: m.region,
            department: 'N/A',
            division: 'N/A',
            amount: m.amount,
            fiscalYear: m.fiscalYear,
            status: m.status,
            processBy: m.processBy,
            processDate: m.processDate
          })),
          ...capitalOutlay.map(c => ({
            id: c._id,
            type: 'Capital Outlay',
            title: c.particulars,
            region: c.region,
            department: c.department,
            division: 'N/A',
            amount: c.cost,
            fiscalYear: c.fiscalYear,
            status: c.status,
            processBy: c.processBy,
            processDate: c.processDate
          })),
          ...cosPersonnel.map(cos => ({
            id: cos._id,
            type: 'COS Personnel',
            title: cos.employeeFullName,
            region: cos.region,
            department: cos.department,
            division: cos.division,
            amount: cos.Total,
            fiscalYear: cos.fiscalYear,
            status: 'Active',
            processBy: cos.processBy,
            processDate: cos.processDate
          }))
        ];

        // Sort by processDate descending
        proposals.sort((a, b) => new Date(b.processDate) - new Date(a.processDate));
        totalCount = proposals.length;

      } else {
        // Get specific category
        let Model, selectFields, typeLabel;

        switch (category) {
          case 'personnel':
            Model = PersonnelServices;
            selectFields = 'employeeFullName region department division fiscalYear Total status processBy processDate';
            typeLabel = 'Personnel Services';
            break;
          case 'mooe':
            Model = MooeProposal;
            selectFields = 'accountingTitle region amount fiscalYear status processBy processDate';
            typeLabel = 'MOOE';
            break;
          case 'capitalOutlay':
            Model = CapitalOutlay;
            selectFields = 'particulars region department cost fiscalYear status processBy processDate';
            typeLabel = 'Capital Outlay';
            break;
          case 'cos':
            Model = COSPersonnel;
            selectFields = 'employeeFullName region department division fiscalYear Total processBy processDate';
            typeLabel = 'COS Personnel';
            break;
          default:
            return res.status(400).json({ message: 'Invalid category' });
        }

        const data = await Model.find(filters)
          .select(selectFields)
          .sort({ processDate: -1 })
          .limit(50)
          .lean();

        totalCount = await Model.countDocuments(filters);

        proposals = data.map(item => ({
          id: item._id,
          type: typeLabel,
          title: item.employeeFullName || item.accountingTitle || item.particulars,
          region: item.region,
          department: item.department || 'N/A',
          division: item.division || 'N/A',
          amount: item.Total || item.amount || item.cost,
          fiscalYear: item.fiscalYear,
          status: item.status || 'Active',
          processBy: item.processBy,
          processDate: item.processDate
        }));
      }

      res.status(200).json({
        message: 'REAL proposals filtered by organizational unit from database',
        dataSource: 'MongoDB Database',
        filters: filters,
        proposals: proposals,
        segregationInfo: {
          totalProposals: totalCount,
          displayedProposals: proposals.length,
          filteredBy: {
            region: region || 'All regions',
            department: department || 'All departments',
            division: division || 'All divisions',
            category: category || 'All categories'
          },
          budgetManagerContext: {
            selectedBy: budgetManagerContext?.selectedBy,
            canSelectAnyOrg: budgetManagerContext?.canSelectAnyOrg || true,
            message: 'Budget Manager viewing REAL data from database'
          },
          organizationalSegregation: {
            working: true,
            dataIsolated: true,
            realDatabaseData: true
          }
        }
      });
    } catch (error) {
      console.error('Error viewing proposals:', error);
      res.status(500).json({ message: 'Server error' });
    }
  }
);

// Example: Budget Manager switching organizational context
budgetManagerOrgRouter.post(
  '/budget-manager/switch-organization',
  ...adminRoute(),
  budgetManagerOrgSelection(),
  logOrgSelection(),
  async (req, res) => {
    try {
      const { newRegion, newDepartment, newDivision } = req.body;
      const budgetManagerContext = req.budgetManagerContext;
      
      // Simulate switching organizational context
      const switchResult = {
        message: 'Organizational context switched successfully',
        previousContext: {
          region: req.query.region || 'Previous region',
          department: req.query.department || 'Previous department',
          division: req.query.division || 'Previous division'
        },
        newContext: {
          region: newRegion,
          department: newDepartment,
          division: newDivision
        },
        switchedBy: {
          userId: budgetManagerContext?.selectedBy,
          role: budgetManagerContext?.selectedByRole,
          timestamp: new Date()
        },
        capabilities: {
          canSwitchAnytime: true,
          canAccessAllOrganizations: true,
          dataSegregationActive: true
        }
      };
      
      res.status(200).json(switchResult);
    } catch (error) {
      console.error('Error switching organization:', error);
      res.status(500).json({ message: 'Server error' });
    }
  }
);

// Get current database organizational distribution
budgetManagerOrgRouter.get(
  '/budget-manager/database-overview',
  ...adminRoute(),
  async (req, res) => {
    try {
      // Import models
      const PersonnelServices = require('../models/PersonnelServices');
      const MooeProposal = require('../models/mooeProposals');
      const CapitalOutlay = require('../models/CapitalOutlay');
      const COSPersonnel = require('../models/COSPersonnel');

      // Get organizational distribution from actual database
      const [
        personnelRegions,
        mooeRegions,
        capitalOutlayRegions,
        cosRegions,
        personnelDepartments,
        capitalOutlayDepartments,
        cosDepartments
      ] = await Promise.all([
        PersonnelServices.distinct('region'),
        MooeProposal.distinct('region'),
        CapitalOutlay.distinct('region'),
        COSPersonnel.distinct('region'),
        PersonnelServices.distinct('department'),
        CapitalOutlay.distinct('department'),
        COSPersonnel.distinct('department')
      ]);

      // Get record counts per organizational unit
      const organizationalStats = {};

      // Combine all unique regions
      const allRegions = [...new Set([...personnelRegions, ...mooeRegions, ...capitalOutlayRegions, ...cosRegions])].filter(Boolean);

      for (const region of allRegions) {
        const [personnelCount, mooeCount, capitalCount, cosCount] = await Promise.all([
          PersonnelServices.countDocuments({ region }),
          MooeProposal.countDocuments({ region }),
          CapitalOutlay.countDocuments({ region }),
          COSPersonnel.countDocuments({ region })
        ]);

        organizationalStats[region] = {
          personnel: personnelCount,
          mooe: mooeCount,
          capitalOutlay: capitalCount,
          cos: cosCount,
          total: personnelCount + mooeCount + capitalCount + cosCount
        };
      }

      // Get total counts
      const [totalPersonnel, totalMooe, totalCapital, totalCOS] = await Promise.all([
        PersonnelServices.countDocuments(),
        MooeProposal.countDocuments(),
        CapitalOutlay.countDocuments(),
        COSPersonnel.countDocuments()
      ]);

      res.status(200).json({
        message: 'Current database organizational distribution',
        dataSource: 'MongoDB Database',
        overview: {
          totalRecords: totalPersonnel + totalMooe + totalCapital + totalCOS,
          byCategory: {
            personnel: totalPersonnel,
            mooe: totalMooe,
            capitalOutlay: totalCapital,
            cos: totalCOS
          }
        },
        organizationalUnits: {
          regions: allRegions,
          departments: [...new Set([...personnelDepartments, ...capitalOutlayDepartments, ...cosDepartments])].filter(Boolean),
          regionsCount: allRegions.length
        },
        organizationalStats: organizationalStats,
        testingInstructions: {
          message: 'Budget Manager can create proposals for any region and test segregation',
          availableRegions: allRegions,
          testSteps: [
            '1. Create proposal for specific region using /budget-manager/create-proposal',
            '2. View proposals filtered by region using /budget-manager/view-proposals?region=REGION_NAME',
            '3. Test segregation using /budget-manager/test-segregation?region=REGION_NAME',
            '4. Verify only data from selected region is returned'
          ]
        }
      });
    } catch (error) {
      console.error('Error getting database overview:', error);
      res.status(500).json({ message: 'Server error' });
    }
  }
);

module.exports = budgetManagerOrgRouter;
