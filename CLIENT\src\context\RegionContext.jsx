import React, { createContext, useState, useContext, useEffect } from 'react';
import api from '../config/api';
import { toast } from 'react-toastify';

// Create the context
const RegionContext = createContext();

// Create a provider component
export const RegionProvider = ({ children }) => {
  const [activeRegion, setActiveRegion] = useState(null);
  const [availableRegions, setAvailableRegions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch available regions on component mount
  useEffect(() => {
    const fetchRegions = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await api.get('/regions');
        
        // Ensure we have an array of regions from database only
        if (response.data && Array.isArray(response.data)) {
          setAvailableRegions(response.data);
          
          // Check if there's a saved region in localStorage
          const savedRegion = localStorage.getItem('activeRegion');
          if (savedRegion) {
            try {
              const parsedRegion = JSON.parse(savedRegion);
              setActiveRegion(parsedRegion);
            } catch (e) {
              console.error('Error parsing saved region:', e);
              localStorage.removeItem('activeRegion');
            }
          } else {
            // If no saved region, set "Central Office" as default
            const centralOffice = response.data.find(region => 
              region.name === "Central Office" || 
              region.regionName === "Central Office"
            );
            
            if (centralOffice) {
              setActiveRegion(centralOffice);
              localStorage.setItem('activeRegion', JSON.stringify(centralOffice));
              toast.info(`Default region set to Central Office`);
            }
          }
        } else if (response.data && typeof response.data === 'object') {
          // If it's an object with regions property
          if (Array.isArray(response.data.regions)) {
            setAvailableRegions(response.data.regions);
            
            // Check if there's a saved region in localStorage
            const savedRegion = localStorage.getItem('activeRegion');
            if (savedRegion) {
              try {
                const parsedRegion = JSON.parse(savedRegion);
                setActiveRegion(parsedRegion);
              } catch (e) {
                console.error('Error parsing saved region:', e);
                localStorage.removeItem('activeRegion');
              }
            } else {
              // If no saved region, set "Central Office" as default
              const centralOffice = response.data.regions.find(region => 
                region.name === "Central Office" || 
                region.regionName === "Central Office"
              );
              
              if (centralOffice) {
                setActiveRegion(centralOffice);
                localStorage.setItem('activeRegion', JSON.stringify(centralOffice));
                toast.info(`Default region set to Central Office`);
              }
            }
          } else {
            // No static fallback - regions must come from database
            console.warn('Regions data is not in expected format, using empty array');
            setAvailableRegions([]);
          }
        } else {
          // No static fallback - regions must come from database
          console.warn('No regions data received from database, using empty array');
          setAvailableRegions([]);
        }
      } catch (err) {
        console.error('Error fetching regions:', err);
        setError('Failed to load regions from database. Please ensure regions are properly configured.');
        toast.error('Failed to load regions from database. Please contact administrator.');

        // No static fallback - regions must come from database only
        setAvailableRegions([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchRegions();
  }, []);

  // Function to change the active region
  const changeActiveRegion = (region) => {
    if (!region) {
      // Handle clearing the region
      setActiveRegion(null);
      localStorage.removeItem('activeRegion');
      toast.info('Active region cleared');
      return;
    }
    
    // Make sure we have a valid region object with at least a name or regionName
    if (!region.name && !region.regionName) {
      console.error('Invalid region object:', region);
      toast.error('Invalid region selected');
      return;
    }
    
    // Clear any existing active region first to avoid UI issues
    setActiveRegion(null);
    
    // Set the new active region after a small delay to ensure UI updates properly
    setTimeout(() => {
      setActiveRegion(region);
      
      // Save to localStorage for persistence
      localStorage.setItem('activeRegion', JSON.stringify(region));
      toast.success(`Active region set to ${region.name || region.regionName}`);
    }, 50);
  };

  // Clear the active region
  const clearActiveRegion = () => {
    setActiveRegion(null);
    localStorage.removeItem('activeRegion');
    toast.info('Active region cleared');
  };

  // The context value that will be provided
  const contextValue = {
    activeRegion,
    availableRegions,
    loading,
    error,
    changeActiveRegion,
    clearActiveRegion
  };

  return (
    <RegionContext.Provider value={contextValue}>
      {children}
    </RegionContext.Provider>
  );
};

// Custom hook to use the region context
export const useRegion = () => {
  const context = useContext(RegionContext);
  if (!context) {
    throw new Error('useRegion must be used within a RegionProvider');
  }
  return context;
};

export default RegionContext;