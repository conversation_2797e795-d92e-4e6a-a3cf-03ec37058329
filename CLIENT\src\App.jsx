import React from "react";
import { Route, Routes } from "react-router-dom";
import UserProvider from "./context/UserContext";
import { RegionProvider } from "./context/RegionContext";
import Dashboard from "./layout/Dashboard";
import HomePage from "./pages/HomePage";
import DashBoardHome from "./pages/DashBoard";
import Proposals from "./pages/Proposals";
import ProposalPage from "./pages/MyProposalPage";
import EmployeeList from "./pages/EmployeeList";
import FiscalYear from "./pages/FiscalPage";
import Categories from "./pages/CategoryPage";
import RataPage from "./pages/RataPage";
import SettingsManagement from "./pages/SettingsPage";
import AnnexPage from "./pages/PS_AnnexesPage";
import SetupPage from "./pages/SetupPage";
import EmployeeCourtAppearancePage from "./pages/CourtAppearancePage";
import OvertimePayDialog from "./pages/EmployeeOvertimePayPage";
import SubsistenceAllowanceMDSPage from "./pages/SubsistenceAllowanceMDSPage";
import SubsistenceAllowanceSTPage from "./pages/SubsistenceAllowanceSTPage";
import LoyaltyPayPage from "./pages/LoyaltyPayPage";
import AppearancePage from "./pages/AppearancePage";
import AllProposalsPage from "./pages/AllProposalsPage";
import ProtectedRoute from "./middleware/ProtectedRoute";
import NotAuthorized from "./pages/NotAuthorizedPage";
import ProposalSummaryPage from "./pages/ProposalSummaryPage";
import FiscalYearSettingsPage from "./pages/FiscalYearSettingsPage";
import CompensationSettingsPage from "./pages/CompensationSettingsPage";
import UploaderPage from "./pages/TestPage";
import MealAllowancePage from "./pages/MealAllowancePage";
import MedicalAllowancePage from "./pages/MedicalAllowancePage";
import ChildrenAllowancePage from "./pages/ChildrenAllowancePage";
import IncomeSubCategoryPage from "./pages/IncomeSubCategoryPage";
import IncomeCategorypage from "./pages/IncomeCategoryPage";
import RetireePage from "./pages/RetireePage";
import UserRegionAssignmentPage from "./pages/UserRegionAssignmentPage";
import SubsidyMonitoringPage from './pages/SubsidyMonitoringPage';
import CapitalOutlayTitleMappingPage from "./pages/CategoryPage";
import RegionSelectionPage from "./pages/RegionSelectionPage";
import CorporateIncomeUploadPage from "./pages/CorporateIncomeUploadPage";
import ConsolidatedCorporateIncomePage from "./pages/ConsolidatedCorporateIncomePage";
import MooeUploadPage from "./pages/MooeUploadPage";
import EmployeeBenefitsPage from "./pages/EmployeeBenefitsPage";
const App = () => {
  return (
    <Routes>
      <Route path="/" element={<HomePage />} />

      <Route
        path="/"
        element={
          <UserProvider>
            <RegionProvider>
              <Dashboard />
            </RegionProvider>
          </UserProvider>
        }
      >
        <Route path="/dashboard" element={<DashBoardHome />} />
        <Route path="/proposal" element={<Proposals />} />
        <Route path="/myProposals" element={<ProposalPage />} />
        <Route path="/employee" element={<EmployeeList />} />
        <Route path="/fiscalYear" element={<FiscalYear />} />
        <Route path="/retirees" element={<EmployeeBenefitsPage />} />

        {/* Protected Routes */}
        <Route
          path="/categories"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER","BUDGET OFFICER"]}>
              <Categories />
            </ProtectedRoute>
          }
        />
        <Route
          path="/rata"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER","BUDGET OFFICER"]}>
              <RataPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/fysettings"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER", "BUDGET OFFICER"]}>
              <FiscalYearSettingsPage />
            </ProtectedRoute>
          }
        />

        <Route
          path="/compsettings"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER", "BUDGET OFFICER"]}>
              <CompensationSettingsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/settings"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER","BUDGET OFFICER"]}>
              <SettingsManagement />
            </ProtectedRoute>
          }
        />
        <Route
          path="/AllProposals"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER","BUDGET OFFICER"]}>
              <AllProposalsPage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/incomesubcategories"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET MANAGER","BUDGET OFFICER"]}>
              <IncomeSubCategoryPage />
            </ProtectedRoute>
          }
        />  
        <Route
          path="/incomcategories"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET MANAGER","BUDGET OFFICER"]}>
              <IncomeCategorypage />
            </ProtectedRoute>
          }
        />
        <Route
          path="/categories"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET MANAGER","BUDGET OFFICER"]}>
              <CapitalOutlayTitleMappingPage />
            </ProtectedRoute>
          }
        />

        <Route path="/not-authorized" element={<NotAuthorized />} />

        <Route path="/annexis" element={<AnnexPage />} />
        <Route path="/employeeBenefits" element={<EmployeeBenefitsPage />} />
        
        {/* Keep these routes for backward compatibility but they will redirect in the future */}
        <Route path="/courtAppearance" element={<EmployeeBenefitsPage />} />
        <Route path="/overTime" element={<EmployeeBenefitsPage />} />
        <Route path="/subsistenceAllowanceMDS" element={<EmployeeBenefitsPage />} />
        <Route path="/subsistenceAllowanceST" element={<EmployeeBenefitsPage />} />
        <Route path="/loyaltyPay" element={<EmployeeBenefitsPage />} />
        <Route path="/Appearance" element={<AppearancePage />} />
        <Route path="/ProposalSummaryPage" element={<ProposalSummaryPage />} />
        <Route path="/uploader" element={<UploaderPage />} />
        <Route path="/mealallowance" element={<EmployeeBenefitsPage />} />
        <Route path="/medicalAllowance" element={<EmployeeBenefitsPage />} />
        <Route path="/childrenAllowance" element={<EmployeeBenefitsPage />} />
        <Route
          path="/user-region-assignments"
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET MANAGER"]}>
              <UserRegionAssignmentPage />
            </ProtectedRoute>
          }
        />
        <Route path="/subsidy-monitoring" element={
          <ProtectedRoute>
            <SubsidyMonitoringPage />
          </ProtectedRoute>
        } />
        
        <Route path="/region-selection" element={<RegionSelectionPage />} />
        <Route 
          path="/corporate-income-upload" 
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER", "BUDGET OFFICER"]}>
              <CorporateIncomeUploadPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/consolidated-corporate-income" 
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER", "BUDGET OFFICER"]}>
              <ConsolidatedCorporateIncomePage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="/mooe-upload" 
          element={
            <ProtectedRoute allowedRoles={["SUPER ADMIN", "BUDGET ADMIN", "BUDGET MANAGER", "BUDGET OFFICER"]}>
              <MooeUploadPage />
            </ProtectedRoute>
          } 
        />

      </Route>
    </Routes>
  );
};

export default App;
