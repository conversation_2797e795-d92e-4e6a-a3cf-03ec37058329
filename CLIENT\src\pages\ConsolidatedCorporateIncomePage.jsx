import { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Container,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  AlertTitle,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  Card,
  CardContent,
  TextField,
  Tab,
  Tabs,
} from '@mui/material';
import { Download, Refresh, PictureAsPdf } from '@mui/icons-material';
import api from '../config/api';
// Import xlsx with named exports
import * as XLSX from 'xlsx';
// Import jsPDF and jsPDF-AutoTable
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

const ConsolidatedCorporateIncomePage = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [reportData, setReportData] = useState(null);
  const [fiscalYear, setFiscalYear] = useState('');
  const [budgetType, setBudgetType] = useState('INITIAL');
  const [fiscalYears, setFiscalYears] = useState([]);
  const [budgetTypes] = useState(['INITIAL', 'NEP', 'GAA']);
  const [activeTab, setActiveTab] = useState(0); // 0 for categories, 1 for subcategories
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  // Fetch available fiscal years on component mount
  useEffect(() => {
    const fetchFiscalYears = async () => {
      try {
        // Try to get fiscal years from settings/active endpoint first
        try {
          const activeResponse = await api.get('/settings/active');
          if (activeResponse.data && activeResponse.data.fiscalYear) {
            console.log("Found active fiscal year:", activeResponse.data.fiscalYear);
            // Use the active fiscal year and also include a few years before and after
            const activeYear = parseInt(activeResponse.data.fiscalYear);
            const years = [
              (activeYear - 2).toString(),
              (activeYear - 1).toString(),
              activeYear.toString(),
              (activeYear + 1).toString(),
              (activeYear + 2).toString()
            ];
            setFiscalYears(years);
            setFiscalYear(activeYear.toString());
            return; // Exit if we successfully got the active fiscal year
          }
        } catch (activeErr) {
          console.log("Could not get active fiscal year:", activeErr);
          // Continue to try other methods
        }
        
        // Try to get all settings
        try {
          const response = await api.get('/settings');
          if (response.data && response.data.data && Array.isArray(response.data.data)) {
            const years = response.data.data.map(setting => setting.fiscalYear);
            const uniqueYears = [...new Set(years)]; // Remove duplicates
            
            if (uniqueYears.length > 0) {
              console.log("Found fiscal years from settings:", uniqueYears);
              setFiscalYears(uniqueYears);
              
              // Set default fiscal year to the most recent one
              const sortedYears = [...uniqueYears].sort((a, b) => b - a);
              setFiscalYear(sortedYears[0]);
              return; // Exit if we successfully got fiscal years
            }
          }
        } catch (settingsErr) {
          console.log("Could not get fiscal years from settings:", settingsErr);
          // Continue to try other methods
        }
        
        // If we're still here, use default years
        const currentYear = new Date().getFullYear();
        const defaultYears = [
          (currentYear - 2).toString(),
          (currentYear - 1).toString(),
          currentYear.toString(),
          (currentYear + 1).toString(),
          (currentYear + 2).toString()
        ];
        console.log("Using default fiscal years:", defaultYears);
        setFiscalYears(defaultYears);
        setFiscalYear(currentYear.toString());
        
      } catch (err) {
        console.error('Error in fiscal year fetching process:', err);
        // Final fallback
        const currentYear = new Date().getFullYear();
        const defaultYears = [
          (currentYear - 2).toString(),
          (currentYear - 1).toString(),
          currentYear.toString(),
          (currentYear + 1).toString(),
          (currentYear + 2).toString()
        ];
        console.log("Using fallback fiscal years:", defaultYears);
        setFiscalYears(defaultYears);
        setFiscalYear(currentYear.toString());
      }
    };

    fetchFiscalYears();
  }, []);

  // Fetch report data
  const fetchReportData = async () => {
    if (!fiscalYear) {
      setError('Please select a fiscal year');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Make sure we're using the correct endpoint path and only sending required parameters
      const params = {
        fiscalYear,
        budgetType
      };
      
      console.log("Sending request to consolidated-corporate-income with params:", params);
      
      const response = await api.get('reports/consolidated-corporate-income', {
        params
      });

      if (response.data) {
        setReportData(response.data);
        setLoading(false);
      } else {
        setError('No data returned from the server. The report may be empty for the selected fiscal year and budget type.');
        setLoading(false);
      }
    } catch (err) {
      console.error('Error fetching report data:', err);
      // More detailed error logging
      if (err.response) {
        console.error('Response data:', err.response.data);
        console.error('Response status:', err.response.status);
      } else if (err.request) {
        console.error('No response received:', err.request);
      }
      
      let errorMessage = 'Failed to fetch report data. Please try again.';
      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.message) {
        errorMessage = `Error: ${err.message}`;
      }
      
      setError(errorMessage);
      setLoading(false);
    }
  };

  // Export to Excel
  const handleExportToExcel = () => {
    if (!reportData) return;

    try {
      // Create workbook
      const wb = XLSX.utils.book_new();
      
      // Export hierarchical worksheet (categories with subcategories)
      const hierarchicalWsData = [];
      
      // Add title row
      hierarchicalWsData.push([`Consolidated Corporate Income Report - Fiscal Year ${reportData.fiscalYear} - ${reportData.budgetType}`]);
      hierarchicalWsData.push([]); // Empty row
      
      // Add header row with regions
      const hierarchicalHeaderRow = ['Income Category / Subcategory'];
      reportData.regions.forEach(region => {
        hierarchicalHeaderRow.push(region);
      });
      hierarchicalHeaderRow.push('Total');
      hierarchicalWsData.push(hierarchicalHeaderRow);
      
      // Add data rows with hierarchical structure
      reportData.hierarchicalData.forEach(category => {
        // Add category row
        const categoryRow = [category.name];
        let categoryTotal = 0;
        
        reportData.regions.forEach(region => {
          const amount = reportData.regionData[region]?.categoryAmounts[category.id] || 0;
          // Use actual number values for Excel
          categoryRow.push(amount);
          categoryTotal += amount;
        });
        
        // Use actual number value for the total
        categoryRow.push(categoryTotal);
        hierarchicalWsData.push(categoryRow);
        
        // Add subcategory rows
        category.subcategories.forEach(subcategory => {
          const subcategoryRow = [`    ${subcategory.name}`]; // Indent with spaces
          let subcategoryTotal = 0;
          
          reportData.regions.forEach(region => {
            const amount = reportData.regionData[region]?.subcategoryAmounts[subcategory.id] || 0;
            // Use actual number values for Excel
            subcategoryRow.push(amount);
            subcategoryTotal += amount;
          });
          
          // Use actual number value for the total
          subcategoryRow.push(subcategoryTotal);
          hierarchicalWsData.push(subcategoryRow);
        });
      });
      
      // Add total row
      const totalRow = ['TOTAL'];
      reportData.regions.forEach(region => {
        // Use actual number values for Excel
        totalRow.push(reportData.regionData[region]?.total || 0);
      });
      totalRow.push(reportData.grandTotal);
      hierarchicalWsData.push(totalRow);
      
      // Create hierarchical worksheet
      const hierarchicalWs = XLSX.utils.aoa_to_sheet(hierarchicalWsData);
      
      // Set column widths
      const hierarchicalColWidths = [
        { wch: 40 }, // Income Category / Subcategory
        ...reportData.regions.map(() => ({ wch: 15 })), // Region columns
        { wch: 15 }, // Total column
      ];
      hierarchicalWs['!cols'] = hierarchicalColWidths;
      
      // Apply number formatting to cells (starting from row 3, which is the data rows)
      const hierarchicalRange = XLSX.utils.decode_range(hierarchicalWs['!ref']);
      for (let R = 3; R <= hierarchicalRange.e.r; R++) {
        for (let C = 1; C <= hierarchicalRange.e.c; C++) {
          const cell_address = { c: C, r: R };
          const cell_ref = XLSX.utils.encode_cell(cell_address);
          if (hierarchicalWs[cell_ref] && typeof hierarchicalWs[cell_ref].v === 'number') {
            hierarchicalWs[cell_ref].z = '#,##0.00'; // Apply number format with 2 decimal places
          }
        }
      }
      
      // Add hierarchical worksheet to workbook
      XLSX.utils.book_append_sheet(wb, hierarchicalWs, 'Consolidated Income');
      
      // Export subcategories worksheet
      // Collect all subcategories from hierarchical data
      const allSubcategories = [];
      if (reportData.hierarchicalData) {
        reportData.hierarchicalData.forEach(category => {
          if (category.subcategories && category.subcategories.length > 0) {
            category.subcategories.forEach(subcategory => {
              allSubcategories.push({
                ...subcategory,
                categoryName: category.name // Add parent category name
              });
            });
          }
        });
      }
      
      if (allSubcategories.length > 0) {
        const subcategoryWsData = [];
        
        // Add title row
        subcategoryWsData.push([`Consolidated Corporate Income Report - Subcategory Details - Fiscal Year ${reportData.fiscalYear} - ${reportData.budgetType}`]);
        subcategoryWsData.push([]); // Empty row
        
        // Add header row with regions
        const subcategoryHeaderRow = ['Income Subcategory', 'Parent Category'];
        reportData.regions.forEach(region => {
          subcategoryHeaderRow.push(region);
        });
        subcategoryHeaderRow.push('Total');
        subcategoryWsData.push(subcategoryHeaderRow);
        
        // Add data rows
        allSubcategories.forEach(subcategory => {
          const row = [subcategory.name, subcategory.categoryName];
          let subcategoryTotal = 0;
          
          reportData.regions.forEach(region => {
            const amount = reportData.regionData[region]?.subcategoryAmounts[subcategory.id] || 0;
            // Use actual number values for Excel
            row.push(amount);
            subcategoryTotal += amount;
          });
          
          // Use actual number value for the total
          row.push(subcategoryTotal);
          subcategoryWsData.push(row);
        });
        
        // Add total row
        const subcategoryTotalRow = ['TOTAL', ''];
        reportData.regions.forEach(region => {
          // Use actual number values for Excel
          subcategoryTotalRow.push(reportData.regionData[region]?.total || 0);
        });
        subcategoryTotalRow.push(reportData.grandTotal);
        subcategoryWsData.push(subcategoryTotalRow);
        
        // Create subcategory worksheet
        const subcategoryWs = XLSX.utils.aoa_to_sheet(subcategoryWsData);
        
        // Set column widths
        const subcategoryColWidths = [
          { wch: 30 }, // Income Subcategory
          { wch: 30 }, // Parent Category
          ...reportData.regions.map(() => ({ wch: 15 })), // Region columns
          { wch: 15 }, // Total column
        ];
        subcategoryWs['!cols'] = subcategoryColWidths;
        
        // Apply number formatting to cells (starting from row 3, which is the data rows)
        const subcategoryRange = XLSX.utils.decode_range(subcategoryWs['!ref']);
        for (let R = 3; R <= subcategoryRange.e.r; R++) {
          for (let C = 2; C <= subcategoryRange.e.c; C++) { // Start from column 2 (after subcategory name and parent category)
            const cell_address = { c: C, r: R };
            const cell_ref = XLSX.utils.encode_cell(cell_address);
            if (subcategoryWs[cell_ref] && typeof subcategoryWs[cell_ref].v === 'number') {
              subcategoryWs[cell_ref].z = '#,##0.00'; // Apply number format with 2 decimal places
            }
          }
        }
        
        // Add subcategory worksheet to workbook
        XLSX.utils.book_append_sheet(wb, subcategoryWs, 'Subcategory Details');
      }
      
      // Generate Excel file and trigger download
      XLSX.writeFile(wb, `Consolidated_Corporate_Income_${reportData.fiscalYear}_${reportData.budgetType}.xlsx`);
    } catch (err) {
      console.error('Error exporting to Excel:', err);
      setError('Failed to export to Excel. Please try again.');
    }
  };
  
  // Export to PDF
  const handleExportToPDF = () => {
    if (!reportData) return;
    
    try {
      // Create new PDF document
      const doc = new jsPDF('landscape');
      
      // Add title
      doc.setFontSize(16);
      doc.text(
        `Consolidated Corporate Income Report - Fiscal Year ${reportData.fiscalYear} - ${reportData.budgetType}`,
        14, 
        15
      );
      
      // Add subtitle based on active tab
      doc.setFontSize(12);
      doc.text(
        activeTab === 0 ? 'By Category' : 'By Subcategory',
        14,
        22
      );
      
      // Add date
      doc.setFontSize(10);
      doc.text(
        `Generated on: ${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
        14,
        28
      );
      
      // Prepare table data based on active tab
      if (activeTab === 0) {
        // Hierarchical table (categories with subcategories)
        const hierarchicalTableData = [];
        
        // Add each category and its subcategories
        reportData.hierarchicalData.forEach(category => {
          // Add category row
          const categoryRow = [{ content: category.name, styles: { fontStyle: 'bold', fillColor: [245, 245, 245] } }];
          const categoryTotal = reportData.categoryTotals[category.id] || 0;
          
          reportData.regions.forEach(region => {
            const amount = reportData.regionData[region]?.categoryAmounts[category.id] || 0;
            categoryRow.push({ 
              content: amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
              styles: { fillColor: [245, 245, 245] }
            });
          });
          
          categoryRow.push({ 
            content: categoryTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
            styles: { fontStyle: 'bold', fillColor: [245, 245, 245] }
          });
          
          hierarchicalTableData.push(categoryRow);
          
          // Add subcategory rows
          category.subcategories.forEach(subcategory => {
            const subcategoryRow = [{ content: '    ' + subcategory.name }];
            const subcategoryTotal = reportData.subcategoryTotals[subcategory.id] || 0;
            
            reportData.regions.forEach(region => {
              const amount = reportData.regionData[region]?.subcategoryAmounts[subcategory.id] || 0;
              subcategoryRow.push(amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            });
            
            subcategoryRow.push(subcategoryTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            
            hierarchicalTableData.push(subcategoryRow);
          });
        });
        
        // Add total row
        const totalRow = [{ content: 'TOTAL', styles: { fontStyle: 'bold' } }];
        reportData.regions.forEach(region => {
          totalRow.push({ 
            content: (reportData.regionData[region]?.total || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
            styles: { fontStyle: 'bold' }
          });
        });
        totalRow.push({ 
          content: reportData.grandTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
          styles: { fontStyle: 'bold' }
        });
        hierarchicalTableData.push(totalRow);
        
        // Create table headers
        const hierarchicalTableHeaders = ['Income Category / Subcategory', ...reportData.regions, 'Total'];
        
        // Add table to PDF using the imported autoTable function
        autoTable(doc, {
          head: [hierarchicalTableHeaders],
          body: hierarchicalTableData,
          startY: 35,
          styles: { fontSize: 8 },
          headStyles: { fillColor: [66, 66, 66] },
          columnStyles: {
            0: { cellWidth: 60 }
          },
          footStyles: { fillColor: [200, 200, 200] }
        });
      } else {
        // Collect all subcategories from hierarchical data
        const allSubcategories = [];
        if (reportData.hierarchicalData) {
          reportData.hierarchicalData.forEach(category => {
            if (category.subcategories && category.subcategories.length > 0) {
              category.subcategories.forEach(subcategory => {
                allSubcategories.push({
                  ...subcategory,
                  categoryName: category.name // Add parent category name
                });
              });
            }
          });
        }
        
        // Subcategories table
        const subcategoryTableData = allSubcategories.map(subcategory => {
          const row = [subcategory.name, subcategory.categoryName];
          let subcategoryTotal = 0;
          
          reportData.regions.forEach(region => {
            const amount = reportData.regionData[region]?.subcategoryAmounts[subcategory.id] || 0;
            row.push(amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
            subcategoryTotal += amount;
          });
          
          row.push(subcategoryTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }));
          return row;
        });
        
        // Add total row
        const totalRow = [{ content: 'TOTAL', colSpan: 2, styles: { fontStyle: 'bold' } }];
        reportData.regions.forEach(region => {
          totalRow.push({ 
            content: (reportData.regionData[region]?.total || 0).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
            styles: { fontStyle: 'bold' }
          });
        });
        totalRow.push({ 
          content: reportData.grandTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
          styles: { fontStyle: 'bold' }
        });
        subcategoryTableData.push(totalRow);
        
        // Create table headers
        const subcategoryTableHeaders = ['Income Subcategory', 'Parent Category', ...reportData.regions, 'Total'];
        
        // Add table to PDF using the imported autoTable function
        autoTable(doc, {
          head: [subcategoryTableHeaders],
          body: subcategoryTableData,
          startY: 35,
          styles: { fontSize: 8 },
          headStyles: { fillColor: [66, 66, 66] },
          alternateRowStyles: { fillColor: [240, 240, 240] },
          columnStyles: {
            0: { cellWidth: 40 },
            1: { cellWidth: 40 }
          },
          footStyles: { fillColor: [200, 200, 200] }
        });
      }
      
      // Add footer
      const pageCount = doc.internal.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(8);
        doc.text(
          `Page ${i} of ${pageCount}`,
          doc.internal.pageSize.width - 20,
          doc.internal.pageSize.height - 10
        );
      }
      
      // Save PDF
      doc.save(`Consolidated_Corporate_Income_${reportData.fiscalYear}_${reportData.budgetType}.pdf`);
    } catch (err) {
      console.error('Error exporting to PDF:', err);
      setError('Failed to export to PDF. Please try again.');
    }
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom>
          Consolidated Corporate Income Report
        </Typography>
        <Typography variant="body1" paragraph>
          View and export consolidated corporate income data across all regions.
        </Typography>

        <Divider sx={{ my: 3 }} />

        <Grid container spacing={3}>
          {/* Filter Controls */}
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="fiscal-year-label">Fiscal Year</InputLabel>
              <Select
                labelId="fiscal-year-label"
                id="fiscal-year"
                value={fiscalYear}
                label="Fiscal Year"
                onChange={(e) => setFiscalYear(e.target.value)}
              >
                {fiscalYears.map((year) => (
                  <MenuItem key={year} value={year}>
                    {year}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth margin="normal">
              <InputLabel id="budget-type-label">Budget Type</InputLabel>
              <Select
                labelId="budget-type-label"
                id="budget-type"
                value={budgetType}
                label="Budget Type"
                onChange={(e) => setBudgetType(e.target.value)}
              >
                {budgetTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          {/* Action Buttons */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={fetchReportData}
                disabled={loading || !fiscalYear}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <Refresh />}
              >
                {loading ? 'Loading...' : 'Generate Report'}
              </Button>
              
              {reportData && (
                <>
                  <Button
                    variant="outlined"
                    color="primary"
                    onClick={handleExportToExcel}
                    startIcon={<Download />}
                  >
                    Export to Excel
                  </Button>
                  
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={handleExportToPDF}
                    startIcon={<PictureAsPdf />}
                  >
                    Export to PDF
                  </Button>
                </>
              )}
            </Box>
          </Grid>

          {/* Error Message */}
          {error && (
            <Grid item xs={12}>
              <Alert severity="error" sx={{ mt: 2 }}>
                <AlertTitle>Error</AlertTitle>
                {error}
              </Alert>
            </Grid>
          )}

          {/* Report Data */}
          {reportData && (
            <Grid item xs={12}>
              <Card sx={{ mt: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Consolidated Corporate Income - FY {reportData.fiscalYear} ({reportData.budgetType})
                  </Typography>
                  
                  {reportData.message ? (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <AlertTitle>No Data Available</AlertTitle>
                      {reportData.message}
                    </Alert>
                  ) : !reportData.hierarchicalData || reportData.hierarchicalData.length === 0 ? (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <AlertTitle>No Categories Found</AlertTitle>
                      No income categories are defined in the system. Please add income categories first.
                    </Alert>
                  ) : !reportData.regions || reportData.regions.length === 0 ? (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <AlertTitle>No Regions Found</AlertTitle>
                      No regions are defined in the system. Please add regions first.
                    </Alert>
                  ) : reportData.grandTotal === 0 ? (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <AlertTitle>No Income Data</AlertTitle>
                      No income data found for the selected fiscal year ({reportData.fiscalYear}) and budget type ({reportData.budgetType}).
                    </Alert>
                  ) : (
                    <>
                      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
                        <Tabs 
                          value={activeTab} 
                          onChange={handleTabChange}
                          aria-label="report tabs"
                        >
                          <Tab label="Hierarchical View" />
                          <Tab label="Subcategory Details" />
                        </Tabs>
                      </Box>
                      
                      {/* Hierarchical View (Categories with Subcategories) */}
                      {activeTab === 0 && (
                        <TableContainer component={Paper} sx={{ mt: 2, overflow: 'auto' }}>
                          <Table size="small" stickyHeader>
                            <TableHead>
                              <TableRow>
                                <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5', width: '300px' }}>
                                  Income Category / Subcategory
                                </TableCell>
                                {reportData.regions.map((region) => (
                                  <TableCell 
                                    key={region} 
                                    align="right"
                                    sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}
                                  >
                                    {region}
                                  </TableCell>
                                ))}
                                <TableCell 
                                  align="right"
                                  sx={{ fontWeight: 'bold', backgroundColor: '#e0e0e0' }}
                                >
                                  Total
                                </TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {reportData.hierarchicalData.map((category) => {
                                // Calculate category total across all regions
                                const categoryTotal = reportData.categoryTotals[category.id] || 0;
                                
                                // Create an array of rows for this category and its subcategories
                                const categoryRows = [];
                                
                                // Add the category row
                                categoryRows.push(
                                  <TableRow key={`category-${category.id}`}>
                                    <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f9f9f9' }}>
                                      {category.name}
                                    </TableCell>
                                    {reportData.regions.map((region) => (
                                      <TableCell key={`${region}-${category.id}`} align="right" sx={{ backgroundColor: '#f9f9f9' }}>
                                        {(reportData.regionData[region]?.categoryAmounts[category.id] || 0)
                                          .toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                      </TableCell>
                                    ))}
                                    <TableCell align="right" sx={{ fontWeight: 'bold', backgroundColor: '#f9f9f9' }}>
                                      {categoryTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                    </TableCell>
                                  </TableRow>
                                );
                                
                                // Add subcategory rows
                                if (category.subcategories && category.subcategories.length > 0) {
                                  category.subcategories.forEach((subcategory) => {
                                    // Calculate subcategory total across all regions
                                    const subcategoryTotal = reportData.subcategoryTotals[subcategory.id] || 0;
                                    
                                    categoryRows.push(
                                      <TableRow key={`subcategory-${category.id}-${subcategory.id}`}>
                                        <TableCell sx={{ paddingLeft: '40px' }}>
                                          {subcategory.name}
                                        </TableCell>
                                        {reportData.regions.map((region) => (
                                          <TableCell key={`${region}-${subcategory.id}`} align="right">
                                            {(reportData.regionData[region]?.subcategoryAmounts[subcategory.id] || 0)
                                              .toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                          </TableCell>
                                        ))}
                                        <TableCell align="right">
                                          {subcategoryTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                        </TableCell>
                                      </TableRow>
                                    );
                                  });
                                }
                                
                                // Return all rows for this category
                                return categoryRows;
                              })}
                              
                              {/* Total Row */}
                              <TableRow sx={{ backgroundColor: '#f0f0f0' }}>
                                <TableCell sx={{ fontWeight: 'bold' }}>
                                  TOTAL
                                </TableCell>
                                {reportData.regions.map((region) => (
                                  <TableCell key={`total-${region}`} align="right" sx={{ fontWeight: 'bold' }}>
                                    {(reportData.regionData[region]?.total || 0)
                                      .toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                  </TableCell>
                                ))}
                                <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                                  {reportData.grandTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                </TableCell>
                              </TableRow>
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                      
                      {/* Subcategory Tab - Flat list of all subcategories */}
                      {activeTab === 1 && (
                        <>
                          {/* Extract all subcategories from hierarchical data */}
                          {(() => {
                            // Collect all subcategories from hierarchical data
                            const allSubcategories = [];
                            if (reportData.hierarchicalData) {
                              reportData.hierarchicalData.forEach(category => {
                                if (category.subcategories && category.subcategories.length > 0) {
                                  category.subcategories.forEach(subcategory => {
                                    allSubcategories.push({
                                      ...subcategory,
                                      categoryName: category.name // Add parent category name
                                    });
                                  });
                                }
                              });
                            }
                            
                            return allSubcategories.length > 0 ? (
                              <TableContainer component={Paper} sx={{ mt: 2, overflow: 'auto' }}>
                                <Table size="small" stickyHeader>
                                  <TableHead>
                                    <TableRow>
                                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5', width: '200px' }}>
                                        Income Subcategory
                                      </TableCell>
                                      <TableCell sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5', width: '200px' }}>
                                        Parent Category
                                      </TableCell>
                                      {reportData.regions.map((region) => (
                                        <TableCell 
                                          key={region} 
                                          align="right"
                                          sx={{ fontWeight: 'bold', backgroundColor: '#f5f5f5' }}
                                        >
                                          {region}
                                        </TableCell>
                                      ))}
                                      <TableCell 
                                        align="right"
                                        sx={{ fontWeight: 'bold', backgroundColor: '#e0e0e0' }}
                                      >
                                        Total
                                      </TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {allSubcategories.map((subcategory) => {
                                      // Calculate subcategory total across all regions
                                      const subcategoryTotal = reportData.subcategoryTotals[subcategory.id] || 0;
                                      
                                      return (
                                        <TableRow key={subcategory.id}>
                                          <TableCell>
                                            {subcategory.name}
                                          </TableCell>
                                          <TableCell>
                                            {subcategory.categoryName}
                                          </TableCell>
                                          {reportData.regions.map((region) => (
                                            <TableCell key={`${region}-${subcategory.id}`} align="right">
                                              {(reportData.regionData[region]?.subcategoryAmounts[subcategory.id] || 0)
                                                .toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                            </TableCell>
                                          ))}
                                          <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                                            {subcategoryTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                          </TableCell>
                                        </TableRow>
                                      );
                                    })}
                                    
                                    {/* Total Row */}
                                    <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                                      <TableCell sx={{ fontWeight: 'bold' }} colSpan={2}>
                                        TOTAL
                                      </TableCell>
                                      {reportData.regions.map((region) => (
                                        <TableCell key={`total-${region}`} align="right" sx={{ fontWeight: 'bold' }}>
                                          {(reportData.regionData[region]?.total || 0)
                                            .toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                        </TableCell>
                                      ))}
                                      <TableCell align="right" sx={{ fontWeight: 'bold' }}>
                                        {reportData.grandTotal.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                                      </TableCell>
                                    </TableRow>
                                  </TableBody>
                                </Table>
                              </TableContainer>
                            ) : (
                              <Alert severity="info" sx={{ mt: 2 }}>
                                <AlertTitle>No Subcategories Found</AlertTitle>
                                No income subcategories are defined in the system or no income data is associated with subcategories.
                              </Alert>
                            );
                          })()}
                        </>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </Paper>
    </Container>
  );
};

export default ConsolidatedCorporateIncomePage;