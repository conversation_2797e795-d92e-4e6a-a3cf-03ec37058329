import React from "react";
import {
  Box,
  Paper,
  Typography,
  Alert,
  Stack,
  useTheme
} from "@mui/material";
import { MdAttach<PERSON>oney, MdCategory } from "react-icons/md";
import IncomeSubCategoriesCustomPage from "../components/incomesubcategories/IncomeSubCategoriesCustomPage";
import TextSearchable from "../global/components/TextSearchable";

const IncomeSubCategoryPage = () => {
  const theme = useTheme();

  const IncomeSubCategorySchema = {
     action: {
      type: "action",
      label: "Actions",
    },
    incomeSubcategoryName: {
        type: "text",
        label: "Income SubCategory Name",
        required: true,
        searchable: true,
        show: true,
    },

  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        sx={{
          p: 3,
          mb: 3,
          background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.secondary.main} 100%)`,
          color: 'white'
        }}
      >
        <Stack direction="row" alignItems="center" spacing={2}>
          <MdCategory size={32} />
          <Box>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Income Subcategories
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9 }}>
              Create and manage income subcategories for detailed revenue classification and budget planning
            </Typography>
          </Box>
        </Stack>
      </Paper>

      {/* Information Alert */}
      <Alert
        severity="info"
        sx={{ mb: 3 }}
        icon={<MdAttachMoney />}
      >
        <Typography variant="body2">
          Create income subcategories here, then use them in <strong>Income Categories</strong> to organize your revenue streams.
          Examples: Tax Revenue, Service Fees, Grants, Interest Income, etc.
        </Typography>
      </Alert>

      {/* Main Content */}
      <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <IncomeSubCategoriesCustomPage
          dataListName="income-subcategories"
          schema={IncomeSubCategorySchema}
          title=""
          description=""
          searchable={false}
        />
      </Paper>
    </Box>
  );
};

export default IncomeSubCategoryPage;