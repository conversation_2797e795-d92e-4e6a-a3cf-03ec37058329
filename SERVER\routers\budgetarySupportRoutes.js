const express = require('express');
const router = express.Router();
const checkToken = require('../middleware/check_token');
const {
  getAllBudgetarySupport,
  getBudgetarySupportById,
  createBudgetarySupport,
  updateBudgetarySupport,
  deleteBudgetarySupport
} = require('../controllers/budgetarySupportController');

// Get all budgetary support entries
router.get('/budgetary-support', checkToken, getAllBudgetarySupport);

// Get budgetary support by ID
router.get('/budgetary-support/:id', checkToken, getBudgetarySupportById);

// Create new budgetary support entry
router.post('/budgetary-support', checkToken, createBudgetarySupport);

// Update budgetary support entry
router.put('/budgetary-support/:id', checkToken, updateBudgetarySupport);

// Delete budgetary support entry
router.delete('/budgetary-support/:id', checkToken, deleteBudgetarySupport);

module.exports = router;


