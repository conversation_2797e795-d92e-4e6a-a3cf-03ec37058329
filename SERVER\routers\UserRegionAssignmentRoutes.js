const {
  getAllUserRegionAssignments,
  getUserRegionAssignments,
  getCurrentUserRegionAssignments,
  createOrUpdateUserRegionAssignment,
  deleteUserRegionAssignment
} = require('../controllers/UserRegionAssignmentController');

const Router = require('express').Router;
const checkToken = require('../middleware/check_token');

const userRegionAssignmentRouter = Router();

// Apply token check middleware to all routes
userRegionAssignmentRouter.use(checkToken);

// Get all user-region assignments
userRegionAssignmentRouter.get('/user-region-assignments', getAllUserRegionAssignments);

// Get assignments for a specific user
userRegionAssignmentRouter.get('/user-region-assignments/:userId', getUserRegionAssignments);

// Get current user's region assignments
userRegionAssignmentRouter.get('/my-region-assignments', getCurrentUserRegionAssignments);

// Create or update user-region assignment
userRegionAssignmentRouter.post('/user-region-assignments', createOrUpdateUserRegionAssignment);

// Delete user-region assignment
userRegionAssignmentRouter.delete('/user-region-assignments/:userId', deleteUserRegionAssignment);

module.exports = userRegionAssignmentRouter;



