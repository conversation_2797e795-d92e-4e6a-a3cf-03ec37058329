import React, { use<PERSON><PERSON>back, useMemo, useState, useEffect } from "react";
import { useRegion } from "../../context/RegionContext";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableFooter,
  Paper,
  Box,
  CircularProgress,
  Typography,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Switch,
  FormControlLabel,
  Tooltip,
  Menu,
  MenuItem,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  FormGroup,
  Divider,
  Alert,
  Snackbar,
  Zoom,
  Fade,
} from "@mui/material";
import { ToastContainer, toast } from "react-toastify";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import StickyButtons from "./StickyButtons";
import MooeRow from "./MooeRow";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";
import "react-toastify/dist/ReactToastify.css";
import { NumericFormat } from "react-number-format";

// Custom component for numeric input formatting
const NumberFormatCustom = React.forwardRef(function NumberFormatCustom(props, ref) {
  const { onChange, ...other } = props;
  return (
    <NumericFormat
      {...other}
      getInputRef={ref}
      thousandSeparator
      decimalScale={2}
      fixedDecimalScale={false} // Don't force decimal places
      allowNegative={false} // Prevent negative values
      onValueChange={(values) => {
        // Only trigger onChange if the value actually changed
        // and is not empty or just formatting
        const numericValue = values.value || "0";
        onChange({
          target: {
            value: numericValue,
          },
        });
      }}
    />
  );
});
// Import icons
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import GetAppIcon from "@mui/icons-material/GetApp";
import SaveIcon from "@mui/icons-material/Save";
import ClearIcon from "@mui/icons-material/Clear";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import EditIcon from "@mui/icons-material/Edit";
import VisibilityIcon from "@mui/icons-material/Visibility";
import BarChartIcon from "@mui/icons-material/BarChart";

const Mooe = ({ onDataChange }) => {
  const { currentUser } = useUser();
  const { activeRegion } = useRegion(); // Get the active region from context
  const queryClient = useQueryClient();
  const [expandedRows, setExpandedRows] = useState([]);
  const [disableIncomeInputs, setDisableIncomeInputs] = useState(false);

  // Enhanced state management
  const [searchTerm, setSearchTerm] = useState("");
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [selectedFilters, setSelectedFilters] = useState({
    sublineItems: [],
    amountRange: { min: "", max: "" },
    hasValues: false,
    emptyValues: false
  });
  const [autoSave, setAutoSave] = useState(false); // Default to false as requested
  const [bulkEditMode, setBulkEditMode] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [viewMode, setViewMode] = useState("table"); // table, chart, summary
  const [showMiniCharts, setShowMiniCharts] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [autoSaveTimer, setAutoSaveTimer] = useState(null);

  // Fetch data using React Query
  const { data, isLoading, error } = useQuery({
    queryKey: ["mooeData", activeRegion?.id], // Add region ID to query key to refetch when region changes
    queryFn: async () => {
      // Add region parameter to the API request
      const params = activeRegion?.id ? { region: activeRegion.id } : {};
      const response = await api.get("/mooe-data", { 
        params,
        withCredentials: true 
      });
      const { entries, status, settings } = response.data;
      const formatted = formatData(entries);

      return {
        formattedData: formatted,
        status: status || "Not Submitted",
        fiscalYear: settings?.fiscalYear || "",
        budgetType: settings?.budgetType || "",
      };
    },
    onError: (err) => {
      console.error("Error fetching MOOE data:", err);
      toast.error("Failed to load data. Please try again.");
    },
  });
  
  // Fetch IAs O&M Cost data
  const { data: iasOMCostData, isLoading: iasOMCostLoading } = useQuery({
    queryKey: ["iasOMCostData", activeRegion?.id], // Add region ID to query key
    queryFn: async () => {
      try {
        // Add region parameter to the API request
        const params = activeRegion?.id ? { region: activeRegion.id } : {};
        const response = await api.get("/ias-om-cost", { 
          params,
          withCredentials: true 
        });
        return response.data;
      } catch (err) {
        console.error("Error fetching IAs O&M Cost data:", err);
        // If the endpoint doesn't exist yet, return default values
        return { nis: 0, cis: 0 };
      }
    }
  });
  
  // State for IAs O&M Cost values
  const [iasOMCost, setIasOMCost] = useState({
    nis: "0",
    cis: "0",
    nisSubsidy: "0",
    cisSubsidy: "0"
  });

  // Save mutation
  const saveMutation = useMutation({
    mutationFn: (payload) => {
      // Include the region ID in the payload
      if (activeRegion?.id) {
        payload.region = activeRegion.id;
      }
      return api.post("/mooe-save", payload, { withCredentials: true });
    },
    onError: (err) => {
      console.error("Save failed:", err);
      toast.error("Failed to save data.");
    },
  });

  const formatData = useCallback((rawData) => {
    // Group by sublineItem but keep all accounting titles visible
    const grouped = {};
    for (let idx = 0; idx < rawData.length; idx++) {
      const item = rawData[idx];
      const key = item.sublineItem || `unknown-${idx}`;
      if (!grouped[key]) {
        grouped[key] = {
          id: key,
          sublineItem: item.sublineItem,
          children: [],
        };
      }
      grouped[key].children.push({
        ...item,
        id: `${key}-${item.uacsCode || "none"}-${idx}`,
      });
    }

    return Object.values(grouped);
  }, []);  // Calculate grand totals including IAs O&M Cost
  const grandTotals = useMemo(() => {
    if (!data?.formattedData) return {
      regularIncome: 0,
      regularSubsidy: 0,
      regularTotal: 0,
      income: 0,
      subsidy: 0,
      total: 0,
      iasIncomeTotal: 0,
      iasSubsidyTotal: 0,
      iasTotal: 0,
      ias: {
        nis: 0,
        cis: 0,
        nisSubsidy: 0,
        cisSubsidy: 0,
        total: 0
      }
    };

    // Calculate regular MOOE totals
    let regularIncome = 0;
    let regularSubsidy = 0;

    data.formattedData.forEach(row => {
      row.children.forEach(child => {
        // For regular MOOE, only include if it's not a special IAs O&M UACS code
        if (child.uacsCode !== "5-02-99-990-NIS" && child.uacsCode !== "5-02-99-990-CIS") {
          regularIncome += parseFloat(child.income || 0);
          regularSubsidy += parseFloat(child.subsidy || 0);
        }
      });
    });

    // Calculate IAs O&M Cost totals from dedicated fields
    const iasNIS = parseFloat(iasOMCost.nis || 0);
    const iasCIS = parseFloat(iasOMCost.cis || 0);
    const iasNISSubsidy = parseFloat(iasOMCost.nisSubsidy || 0);
    const iasCISSubsidy = parseFloat(iasOMCost.cisSubsidy || 0);

    const iasIncomeTotal = iasNIS + iasCIS;
    const iasSubsidyTotal = iasNISSubsidy + iasCISSubsidy;
    const iasTotal = iasIncomeTotal + iasSubsidyTotal;

    // Calculate combined totals
    const totalIncome = regularIncome + iasIncomeTotal;
    const totalSubsidy = regularSubsidy + iasSubsidyTotal;
    const regularTotal = regularIncome + regularSubsidy;
    const total = totalIncome + totalSubsidy;
    
    return { 
      regularIncome,
      regularSubsidy,
      regularTotal,
      income: totalIncome, 
      subsidy: totalSubsidy, 
      total,
      iasIncomeTotal,
      iasSubsidyTotal,
      iasTotal,
      ias: {
        nis: iasNIS,
        cis: iasCIS,
        nisSubsidy: iasNISSubsidy,
        cisSubsidy: iasCISSubsidy,
        total: iasTotal
      }
    };
  }, [data?.formattedData, iasOMCost]);

  // Keep all rows collapsed by default for better performance
  // Removed auto-expand functionality to improve loading speed

  // Calculate total function (moved up to avoid hoisting issues)
  const calculateTotal = useCallback((children) => {
    return children.reduce((sum, child) => {
      const income = parseFloat(child.income || 0);
      const subsidy = parseFloat(child.subsidy || 0);
      return sum + income + subsidy;
    }, 0);
  }, []);

  // Enhanced filtering logic
  const filteredData = useMemo(() => {
    if (!data?.formattedData) return [];

    return data.formattedData.filter(row => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSubline = row.sublineItem?.toLowerCase().includes(searchLower);
        const matchesChildren = row.children.some(child =>
          child.accountingTitle?.toLowerCase().includes(searchLower) ||
          child.uacsCode?.toLowerCase().includes(searchLower)
        );
        if (!matchesSubline && !matchesChildren) return false;
      }

      // Subline item filter
      if (selectedFilters.sublineItems.length > 0) {
        if (!selectedFilters.sublineItems.includes(row.sublineItem)) return false;
      }

      // Amount range filter
      if (selectedFilters.amountRange.min || selectedFilters.amountRange.max) {
        const totalAmount = calculateTotal(row.children);
        const min = parseFloat(selectedFilters.amountRange.min) || 0;
        const max = parseFloat(selectedFilters.amountRange.max) || Infinity;
        if (totalAmount < min || totalAmount > max) return false;
      }

      // Has values filter
      if (selectedFilters.hasValues) {
        const hasValues = row.children.some(child =>
          parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0
        );
        if (!hasValues) return false;
      }

      // Empty values filter
      if (selectedFilters.emptyValues) {
        const hasValues = row.children.some(child =>
          parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0
        );
        if (hasValues) return false;
      }

      return true;
    });
  }, [data?.formattedData, searchTerm, selectedFilters, calculateTotal]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSave && hasUnsavedChanges) {
      if (autoSaveTimer) clearTimeout(autoSaveTimer);

      const timer = setTimeout(() => {
        handleSave(true); // true indicates auto-save
      }, 3000); // Auto-save after 3 seconds of inactivity

      setAutoSaveTimer(timer);

      return () => clearTimeout(timer);
    }
  }, [autoSave, hasUnsavedChanges]);
  
  // Update IAs O&M Cost state when data is loaded
  useEffect(() => {
    if (iasOMCostData) {
      setIasOMCost({
        nis: iasOMCostData.nis.toString(),
        cis: iasOMCostData.cis.toString(),
        nisSubsidy: iasOMCostData.nisSubsidy ? iasOMCostData.nisSubsidy.toString() : "0",
        cisSubsidy: iasOMCostData.cisSubsidy ? iasOMCostData.cisSubsidy.toString() : "0"
      });
    }
  }, [iasOMCostData]);
  
  // Refetch data when region changes
  useEffect(() => {
    if (activeRegion) {
      // Invalidate queries to trigger refetch with new region
      queryClient.invalidateQueries(["mooeData"]);
      queryClient.invalidateQueries(["iasOMCostData"]);
    }
  }, [activeRegion, queryClient]);

  // Track unsaved changes
  const trackChanges = useCallback(() => {
    setHasUnsavedChanges(true);
  }, []);

  const toggleExpandRow = useCallback((rowId) => {
    setExpandedRows(prev => 
      prev.includes(rowId) 
        ? prev.filter(id => id !== rowId)
        : [...prev, rowId]
    );
  }, []);

  const handleAmountChange = useCallback((rowId, childId, value) => {
    // Validate input
    const numValue = parseFloat(value) || 0;
    if (numValue < 0) {
      setValidationErrors(prev => ({
        ...prev,
        [`${rowId}-${childId}-amount`]: "Amount cannot be negative"
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${rowId}-${childId}-amount`];
        return newErrors;
      });
    }

    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;

      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;

          return {
            ...row,
            children: row.children.map(child =>
              child.id === childId ? { ...child, amount: value } : child
            )
          };
        })
      };
    });

    trackChanges();
  }, [queryClient, trackChanges]);

  const handleIncomeChange = useCallback((rowId, childId, value) => {
    // If income inputs are disabled, don't allow changes
    if (disableIncomeInputs) {
      toast.warning("Income fields are locked because the total matches the Corporate Income projection. To make changes, adjust the Corporate Income first.");
      return;
    }

    // Don't convert empty string to 0 - preserve the actual input
    const stringValue = value === "" ? "" : value;
    const numValue = parseFloat(stringValue);

    // Only validate if there's actually a value
    if (stringValue !== "" && (isNaN(numValue) || numValue < 0)) {
      setValidationErrors(prev => ({
        ...prev,
        [`${rowId}-${childId}-income`]: "Income must be a valid positive number"
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${rowId}-${childId}-income`];
        return newErrors;
      });
    }

    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;

      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;

          return {
            ...row,
            children: row.children.map(child =>
              child.id === childId ? {
                ...child,
                income: stringValue === "" ? "0" : stringValue,
                // Auto-compute amount when income changes
                amount: (parseFloat(stringValue || 0) + parseFloat(child.subsidy || 0)).toString()
              } : child
            )
          };
        })
      };
    });

    trackChanges();
    
    // Trigger auto-save when changes are made
    if (autoSave) {
      if (autoSaveTimer) clearTimeout(autoSaveTimer);
      setAutoSaveTimer(setTimeout(() => {
        handleSave(true);
      }, 3000));
    }
  }, [queryClient, disableIncomeInputs, trackChanges, autoSave, autoSaveTimer, handleSave]);

  const handleSubsidyChange = useCallback((rowId, childId, value) => {
    // Don't convert empty string to 0 - preserve the actual input
    const stringValue = value === "" ? "" : value;
    const numValue = parseFloat(stringValue);

    // Only validate if there's actually a value
    if (stringValue !== "" && (isNaN(numValue) || numValue < 0)) {
      setValidationErrors(prev => ({
        ...prev,
        [`${rowId}-${childId}-subsidy`]: "Subsidy must be a valid positive number"
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`${rowId}-${childId}-subsidy`];
        return newErrors;
      });
    }

    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;

      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;

          return {
            ...row,
            children: row.children.map(child =>
              child.id === childId ? {
                ...child,
                subsidy: stringValue === "" ? "0" : stringValue,
                // Auto-compute amount when subsidy changes
                amount: (parseFloat(child.income || 0) + parseFloat(stringValue || 0)).toString()
              } : child
            )
          };
        })
      };
    });

    trackChanges();
    
    // Trigger auto-save when changes are made
    if (autoSave) {
      if (autoSaveTimer) clearTimeout(autoSaveTimer);
      setAutoSaveTimer(setTimeout(() => {
        handleSave(true);
      }, 3000));
    }
  }, [queryClient, trackChanges, autoSave, autoSaveTimer, handleSave]);

  const handleTitleChange = useCallback((rowId, childId, value) => {
    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;
      
      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;
          
          return {
            ...row,
            children: row.children.map(child => 
              child.id === childId ? { ...child, accountingTitle: value } : child
            )
          };
        })
      };
    });
  }, [queryClient]);
  
  // Handlers for IAs O&M Cost input fields
  const handleIAsOMCostChange = (field) => (event) => {
    let value = event.target.value;
    
    // Don't convert empty string to 0 - preserve the actual input
    const stringValue = value === "" ? "" : value;
    const numValue = parseFloat(stringValue);
    
    // Only validate if there's actually a value
    if (stringValue !== "" && (isNaN(numValue) || numValue < 0)) {
      setValidationErrors(prev => ({
        ...prev,
        [`ias-${field}`]: `${field.toUpperCase()} must be a valid positive number`
      }));
      return;
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[`ias-${field}`];
        return newErrors;
      });
    }
    
    setIasOMCost(prev => ({ ...prev, [field]: stringValue === "" ? "0" : stringValue }));
    setHasUnsavedChanges(true);
  };

  const addCustomMOOE = useCallback((rowId) => {
    queryClient.setQueryData(["mooeData"], (old) => {
      if (!old) return old;
      
      return {
        ...old,
        formattedData: old.formattedData.map(row => {
          if (row.id !== rowId) return row;
          
          // Find the Other MOOE category to get its details
          const otherMooeItem = row.children.find(child => child.uacsCode === "5-02-99-990");
          
          if (!otherMooeItem) return row;
          
          return {
            ...row,
            children: [
              ...row.children,
              {
                id: `${rowId}-custom-${Date.now()}`,
                accountingTitle: "Custom MOOE",
                uacsCode: "5-02-99-990",
                amount: "0",
                income: "0",
                subsidy: "0",
                custom: true,
                sublineItem: row.sublineItem
              }
            ]
          };
        })
      };
    });
  }, [queryClient]);

  const handleSave = (isAutoSave = false) => {
    if (!data?.formattedData) return;

    // Check for validation errors
    if (Object.keys(validationErrors).length > 0) {
      if (!isAutoSave) {
        toast.error("Please fix validation errors before saving.");
      }
      return;
    }
    
    // Get the current data from queryClient
    const currentData = queryClient.getQueryData(["mooeData", activeRegion?.id]);
    
    // If income inputs are disabled, preserve the original income values
    let entries;
    if (disableIncomeInputs && currentData?.formattedData) {
      // Create a map of original income values
      const originalIncomeMap = {};
      currentData.formattedData.forEach(row => {
        row.children.forEach(child => {
          const key = `${row.id}-${child.id}`;
          originalIncomeMap[key] = child.income;
        });
      });
      
      // Use original income values but updated subsidy values
      entries = data.formattedData
        .flatMap(row =>
          row.children.map(child => {
            const key = `${row.id}-${child.id}`;
            const originalIncome = originalIncomeMap[key] || child.income;

            // Determine if this is a new entry (has values but no existing status)
            const hasValues = parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0;
            const isNewEntry = hasValues && !child.status;

            return {
              ...child,
              sublineItem: row.sublineItem,
              status: isNewEntry ? "Not Submitted" : (child.status || data?.status),
              income: disableIncomeInputs ? parseFloat(originalIncome || 0) : parseFloat(child.income || 0),
              subsidy: parseFloat(child.subsidy || 0),
              amount: disableIncomeInputs
                ? parseFloat(originalIncome || 0) + parseFloat(child.subsidy || 0)
                : parseFloat(child.income || 0) + parseFloat(child.subsidy || 0)
            };
          })
        )
        .filter(entry => 
          parseFloat(entry.income || 0) !== 0 || parseFloat(entry.subsidy || 0) !== 0
        );
    } else {
      // Normal processing if income inputs are not disabled
      entries = data.formattedData
        .flatMap(row =>
          row.children.map(child => {
            const hasValues = parseFloat(child.income || 0) > 0 || parseFloat(child.subsidy || 0) > 0;
            const isNewEntry = hasValues && !child.status;

            return {
              ...child,
              sublineItem: row.sublineItem,
              status: isNewEntry ? "Not Submitted" : (child.status || data?.status),
              income: parseFloat(child.income || 0),
              subsidy: parseFloat(child.subsidy || 0),
              amount: parseFloat(child.income || 0) + parseFloat(child.subsidy || 0)
            };
          })
        )
        .filter(entry => 
          parseFloat(entry.income || 0) !== 0 || parseFloat(entry.subsidy || 0) !== 0
        );
    }

    // Add IAs O&M Cost entries if they have values
    const iasOMCostEntries = [];
    
    // Add NIS entry if it has values
    if (parseFloat(iasOMCost.nis || 0) > 0 || parseFloat(iasOMCost.nisSubsidy || 0) > 0) {
      iasOMCostEntries.push({
        sublineItem: "Irrigators' Associations (IAs) Operation & Maintenance Cost",
        accountingTitle: "National Irrigation System",
        uacsCode: "5-02-99-990-NIS",
        income: parseFloat(iasOMCost.nis || 0),
        subsidy: parseFloat(iasOMCost.nisSubsidy || 0),
        amount: parseFloat(iasOMCost.nis || 0) + parseFloat(iasOMCost.nisSubsidy || 0),
        status: data?.status || "Not Submitted"
      });
    }
    
    // Add CIS entry if it has values
    if (parseFloat(iasOMCost.cis || 0) > 0 || parseFloat(iasOMCost.cisSubsidy || 0) > 0) {
      iasOMCostEntries.push({
        sublineItem: "Irrigators' Associations (IAs) Operation & Maintenance Cost",
        accountingTitle: "Communal Irrigation System",
        uacsCode: "5-02-99-990-CIS",
        income: parseFloat(iasOMCost.cis || 0),
        subsidy: parseFloat(iasOMCost.cisSubsidy || 0),
        amount: parseFloat(iasOMCost.cis || 0) + parseFloat(iasOMCost.cisSubsidy || 0),
        status: data?.status || "Not Submitted"
      });
    }

    const payload = {
      meta: {
        processBy: `${currentUser.FirstName} ${currentUser.LastName}`,
        processDate: new Date(),
        region: activeRegion?.id || currentUser.Region, // Use the active region from context
        fiscalYear: data?.fiscalYear,
        budgetType: data?.budgetType,
        status: data?.status,
      },
      entries: [...entries, ...iasOMCostEntries],
    };
    
    // Save all MOOE data including IAs O&M Cost entries
    saveMutation.mutate(payload, {
      onSuccess: (response) => {
        console.log("Save response:", response.data);
        setHasUnsavedChanges(false);
        setLastSaved(new Date());
        
        if (isAutoSave) {
          toast.success("Auto-saved successfully!", { autoClose: 2000 });
        } else {
          toast.success("Data saved successfully!");
        }
        
        // Refresh the data after saving
        setTimeout(() => {
          queryClient.invalidateQueries(["mooeData"]);
          queryClient.invalidateQueries(["consolidatedSummary"]);
        }, 500);
      }
    });
  };

  const handleClear = () => {
    queryClient.invalidateQueries(["mooeData"]);
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="300px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Box>Error loading data</Box>;
  }

  const isEditable = ["Not Submitted", "Returned", "Draft"].includes(
    data?.status
  );

  // Enhanced toolbar functions
  const handleExportToExcel = () => {
    // Implementation for Excel export
    const exportData = filteredData.flatMap(row =>
      row.children.map(child => ({
        'Subline Item': row.sublineItem,
        'Accounting Title': child.accountingTitle,
        'UACS Code': child.uacsCode,
        'Income': parseFloat(child.income || 0),
        'Subsidy': parseFloat(child.subsidy || 0),
        'Amount': parseFloat(child.income || 0) + parseFloat(child.subsidy || 0)
      }))
    );

    // Create CSV content
    const headers = Object.keys(exportData[0] || {});
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => headers.map(header => `"${row[header]}"`).join(','))
    ].join('\n');

    // Download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `MOOE_Data_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    toast.success("Data exported successfully!");
  };

  const handleBulkEdit = () => {
    setBulkEditMode(!bulkEditMode);
    setSelectedRows([]);
  };

  const toggleExpandAll = () => {
    if (expandedRows.length === filteredData.length) {
      setExpandedRows([]);
    } else {
      setExpandedRows(filteredData.map(row => row.id));
    }
  };

  return (
    <>
      {/* Enhanced Toolbar */}
      <Box sx={{ mb: 3 }}>
        {/* Top Row - Main Controls */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center" gap={2}>
            <StickyButtons
              handleSave={() => handleSave(false)}
              handleClear={handleClear}
              disabled={!isEditable}
            />

            {/* Auto-save toggle */}
            <FormControlLabel
              control={
                <Switch
                  checked={autoSave}
                  onChange={(e) => setAutoSave(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box display="flex" alignItems="center" gap={1}>
                  <SaveIcon fontSize="small" />
                  <Typography variant="body2">Auto-save</Typography>
                </Box>
              }
            />

            {/* Last saved indicator */}
            {lastSaved && (
              <Chip
                size="small"
                label={`Last saved: ${lastSaved.toLocaleTimeString()}`}
                color="success"
                variant="outlined"
              />
            )}

            {/* Unsaved changes indicator */}
            {hasUnsavedChanges && (
              <Chip
                size="small"
                label="Unsaved changes"
                color="warning"
                variant="outlined"
              />
            )}
          </Box>

          <Box display="flex" alignItems="center" gap={1}>
            {/* View mode toggle */}
            <Tooltip title="Toggle view mode">
              <IconButton
                onClick={() => setViewMode(viewMode === "table" ? "chart" : "table")}
                color={viewMode === "chart" ? "primary" : "default"}
              >
                {viewMode === "table" ? <BarChartIcon /> : <VisibilityIcon />}
              </IconButton>
            </Tooltip>

            {/* Export button */}
            <Tooltip title="Export to Excel">
              <IconButton onClick={handleExportToExcel} color="primary">
                <GetAppIcon />
              </IconButton>
            </Tooltip>

            {/* Bulk edit toggle */}
            <Tooltip title="Bulk edit mode">
              <IconButton
                onClick={handleBulkEdit}
                color={bulkEditMode ? "primary" : "default"}
              >
                <EditIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Second Row - Search and Filters */}
        <Box display="flex" alignItems="center" gap={2} mb={2}>
          {/* Search */}
          <TextField
            size="small"
            placeholder="Search subline items, accounting titles, or UACS codes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 300 }}
          />

          {/* Filter button */}
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={(e) => setFilterAnchorEl(e.currentTarget)}
            color={Object.values(selectedFilters).some(f =>
              Array.isArray(f) ? f.length > 0 : f !== "" && f !== false
            ) ? "primary" : "inherit"}
          >
            Filters
          </Button>

          {/* Expand/Collapse All */}
          <Button
            variant="outlined"
            startIcon={expandedRows.length === filteredData.length ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            onClick={toggleExpandAll}
          >
            {expandedRows.length === filteredData.length ? "Collapse All" : "Expand All"}
          </Button>

          {/* Results count */}
          <Typography variant="body2" color="text.secondary">
            Showing {filteredData.length} of {data?.formattedData?.length || 0} categories
          </Typography>
        </Box>

        {/* Validation errors alert */}
        {Object.keys(validationErrors).length > 0 && (
          <Alert severity="error" sx={{ mb: 2 }}>
            Please fix the following errors: {Object.values(validationErrors).join(", ")}
          </Alert>
        )}
      </Box>

      {/* Main Table with Zoom Animation */}
      <Zoom in={true} timeout={600}>
        <Box sx={{ position: 'relative' }}>
          <TableContainer
            component={Paper}
            sx={{
              maxHeight: "calc(70vh - 50px)", // Reduced height to make room for footer
              overflow: "auto",
              borderRadius: '8px',
              border: '1px solid rgba(224, 224, 224, 1)',
              boxShadow: '0 8px 32px rgba(0,0,0,0.1)',
              transition: 'all 0.3s ease',
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#c1c1c1',
                borderRadius: '4px',
                '&:hover': {
                  background: '#a8a8a8',
                },
              },
            }}
          >
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                <TableCell width="5%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                  borderLeft: "1px solid white",
                }}></TableCell>
                <TableCell width="20%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                }}>SUBLINE ITEM</TableCell>
                <TableCell width="25%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                }}>ACCOUNTING TITLE</TableCell>
                <TableCell width="10%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                }}>UACS CODE</TableCell>
                <TableCell width="12%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                  textAlign: "right"
                }}>INCOME</TableCell>
                <TableCell width="12%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                  textAlign: "right"
                }}>SUBSIDY</TableCell>
                <TableCell width="16%" sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderBottom: "1px solid white",
                  borderTop: "1px solid white",
                  borderRight: "1px solid white",
                  textAlign: "right"
                }}>AMOUNT</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredData?.map((row, index) => (
                <MooeRow
                  key={row.id}
                  row={row}
                  expanded={expandedRows.includes(row.id)}
                  onToggleExpand={toggleExpandRow}
                  onAmountChange={handleAmountChange}
                  onTitleChange={handleTitleChange}
                  onAddCustomMOOE={addCustomMOOE}
                  calculateTotal={calculateTotal}
                  status={data?.status}
                  onIncomeChange={handleIncomeChange}
                  onSubsidyChange={handleSubsidyChange}
                  disableIncomeInputs={disableIncomeInputs}
                />
              ))}
              
              {/* IRRIGATORS' ASSOCIATIONS (IAs) OPERATION & MAINTENANCE COST section */}
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell colSpan={7} sx={{ fontWeight: 'bold', padding: '16px', borderBottom: '2px solid #375e38' }}>
                  IRRIGATORS' ASSOCIATIONS (IAs) OPERATION & MAINTENANCE COST
                </TableCell>
              </TableRow>
              
              {/* NIS Row */}
              <TableRow>
                <TableCell sx={{ width: '5%' }}></TableCell>
                <TableCell sx={{ width: '20%' }}></TableCell>
                <TableCell sx={{ width: '25%' }}>NIS (National Irrigation System)</TableCell>
                <TableCell sx={{ width: '10%' }}></TableCell>
                <TableCell sx={{ width: '12%' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={iasOMCost.nis === "0" || iasOMCost.nis === 0 ? "" : (iasOMCost.nis || "")}
                    onChange={handleIAsOMCostChange('nis')}
                    disabled={!isEditable}
                    placeholder="0.00"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      inputComponent: NumberFormatCustom,
                      inputProps: { style: { textAlign: 'right' } }
                    }}
                    sx={{
                      backgroundColor: !isEditable ? "#f5f5f5" : "white",
                      '& .MuiInputBase-input': {
                        textAlign: 'right'
                      }
                    }}
                  />
                </TableCell>
                <TableCell sx={{ width: '12%' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={iasOMCost.nisSubsidy === "0" || iasOMCost.nisSubsidy === 0 ? "" : (iasOMCost.nisSubsidy || "")}
                    onChange={handleIAsOMCostChange('nisSubsidy')}
                    disabled={!isEditable}
                    placeholder="0.00"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      inputComponent: NumberFormatCustom,
                      inputProps: { style: { textAlign: 'right' } }
                    }}
                    sx={{
                      backgroundColor: !isEditable ? "#f5f5f5" : "white",
                      '& .MuiInputBase-input': {
                        textAlign: 'right'
                      }
                    }}
                  />
                </TableCell>
                <TableCell sx={{ width: '16%', textAlign: 'right' }}>
                  ₱{(parseFloat(iasOMCost.nis || 0) + parseFloat(iasOMCost.nisSubsidy || 0)).toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
              </TableRow>
              
              {/* CIS Row */}
              <TableRow>
                <TableCell sx={{ width: '5%' }}></TableCell>
                <TableCell sx={{ width: '20%' }}></TableCell>
                <TableCell sx={{ width: '25%' }}>CIS (Communal Irrigation System)</TableCell>
                <TableCell sx={{ width: '10%' }}></TableCell>
                <TableCell sx={{ width: '12%' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={iasOMCost.cis === "0" || iasOMCost.cis === 0 ? "" : (iasOMCost.cis || "")}
                    onChange={handleIAsOMCostChange('cis')}
                    disabled={!isEditable}
                    placeholder="0.00"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      inputComponent: NumberFormatCustom,
                      inputProps: { style: { textAlign: 'right' } }
                    }}
                    sx={{
                      backgroundColor: !isEditable ? "#f5f5f5" : "white",
                      '& .MuiInputBase-input': {
                        textAlign: 'right'
                      }
                    }}
                  />
                </TableCell>
                <TableCell sx={{ width: '12%' }}>
                  <TextField
                    fullWidth
                    size="small"
                    value={iasOMCost.cisSubsidy === "0" || iasOMCost.cisSubsidy === 0 ? "" : (iasOMCost.cisSubsidy || "")}
                    onChange={handleIAsOMCostChange('cisSubsidy')}
                    disabled={!isEditable}
                    placeholder="0.00"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₱</InputAdornment>,
                      inputComponent: NumberFormatCustom,
                      inputProps: { style: { textAlign: 'right' } }
                    }}
                    sx={{
                      backgroundColor: !isEditable ? "#f5f5f5" : "white",
                      '& .MuiInputBase-input': {
                        textAlign: 'right'
                      }
                    }}
                  />
                </TableCell>
                <TableCell sx={{ width: '16%', textAlign: 'right' }}>
                  ₱{(parseFloat(iasOMCost.cis || 0) + parseFloat(iasOMCost.cisSubsidy || 0)).toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
              </TableRow>
              
              {/* IAs O&M Cost Total Row */}
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell colSpan={4} sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                  MOOE - O & M COST (IAs):
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                  ₱{grandTotals.iasIncomeTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                  ₱{grandTotals.iasSubsidyTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                  ₱{grandTotals.iasTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>

          {/* Fixed Footer Table with Animation */}
          <Fade in={true} timeout={1000}>
            <Table
              sx={{
                position: 'sticky',
                bottom: 0,
                width: '100%',
                tableLayout: 'fixed',
                borderCollapse: 'separate',
                borderSpacing: 0,
                marginTop: '-2px', // To avoid gap between tables
                zIndex: 2,
                boxShadow: '0 -2px 8px rgba(0,0,0,0.1)',
                transition: 'all 0.3s ease'
              }}
            >
          <TableFooter>
            {/* Regular MOOE Total Row */}
            <TableRow>
              <TableCell 
                colSpan={4} 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderLeft: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "60%",
                }}
              >
                REGULAR MOOE TOTAL:
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.regularIncome.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.regularSubsidy.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderTop: "1px solid white",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "16%",
                }}
              >
                ₱{grandTotals.regularTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
            </TableRow>
            
            {/* IAs O&M Cost Total Row */}
            <TableRow>
              <TableCell 
                colSpan={4} 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderLeft: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "60%",
                }}
              >
                MOOE - O & M COST (IAs):
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.iasIncomeTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.iasSubsidyTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderTop: "1px solid white",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "16%",
                }}
              >
                ₱{grandTotals.iasTotal.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
            </TableRow>
            
            {/* Grand Total Row */}
            <TableRow>
              <TableCell 
                colSpan={4} 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderLeft: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "60%",
                }}
              >
                TOTAL MOOE REGULAR + IAs O & M COST:
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.income.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderRight: "1px solid white",
                  borderTop: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "12%",
                }}
              >
                ₱{grandTotals.subsidy.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
              <TableCell 
                sx={{ 
                  backgroundColor: "#375e38", 
                  color: "#fff",
                  fontWeight: "bold",
                  padding: "12px 16px",
                  borderTop: "1px solid white",
                  borderRight: "1px solid white",
                  borderBottom: "1px solid white",
                  textAlign: "right",
                  width: "16%",
                }}
              >
                ₱{grandTotals.total.toLocaleString(undefined, { minimumFractionDigits: 2 })}
              </TableCell>
            </TableRow>
          </TableFooter>
            </Table>
          </Fade>
        </Box>
      </Zoom>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={() => setFilterAnchorEl(null)}
        PaperProps={{ sx: { minWidth: 300, p: 2 } }}
      >
        <Typography variant="h6" gutterBottom>Filters</Typography>
        <Divider sx={{ mb: 2 }} />

        {/* Amount Range Filter */}
        <Typography variant="subtitle2" gutterBottom>Amount Range</Typography>
        <Box display="flex" gap={1} mb={2}>
          <TextField
            size="small"
            label="Min"
            type="number"
            value={selectedFilters.amountRange.min}
            onChange={(e) => setSelectedFilters(prev => ({
              ...prev,
              amountRange: { ...prev.amountRange, min: e.target.value }
            }))}
          />
          <TextField
            size="small"
            label="Max"
            type="number"
            value={selectedFilters.amountRange.max}
            onChange={(e) => setSelectedFilters(prev => ({
              ...prev,
              amountRange: { ...prev.amountRange, max: e.target.value }
            }))}
          />
        </Box>

        {/* Value Filters */}
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                checked={selectedFilters.hasValues}
                onChange={(e) => setSelectedFilters(prev => ({
                  ...prev,
                  hasValues: e.target.checked,
                  emptyValues: e.target.checked ? false : prev.emptyValues
                }))}
              />
            }
            label="Show only entries with values"
          />          <FormControlLabel
            control={
              <Checkbox
                checked={selectedFilters.emptyValues}
                onChange={(e) => setSelectedFilters(prev => ({
                  ...prev,
                  emptyValues: e.target.checked,
                  hasValues: e.target.checked ? false : prev.hasValues
                }))}
              />
            }
            label="Show only empty entries"
          />
        </FormGroup>

        <Box display="flex" justifyContent="space-between" mt={2}>
          <Button
            size="small"
            onClick={() => setSelectedFilters({
              sublineItems: [],
              amountRange: { min: "", max: "" },
              hasValues: false,
              emptyValues: false
            })}
          >
            Clear All
          </Button>
          <Button
            size="small"
            variant="contained"
            onClick={() => setFilterAnchorEl(null)}
          >
            Apply
          </Button>
        </Box>
      </Menu>

      {/* Enhanced Toast Container */}
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </>
  );
};

export default Mooe;
