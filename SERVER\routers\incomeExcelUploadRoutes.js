const express = require("express");
const multer = require("multer");
const router = express.Router();
const incomeExcelUploadController = require("../controllers/incomeExcelUploadController");

// Configure Multer (Store file in memory)
const storage = multer.memoryStorage();
const upload = multer({ storage });

// Route for uploading corporate income Excel file
router.post("/corporate-income/upload", upload.single("file"), incomeExcelUploadController.uploadCorporateIncome);

// Route for downloading sample template
router.get("/corporate-income/template", incomeExcelUploadController.getSampleTemplate);

module.exports = router;