const IncomeSubcategory = require("../models/IncomeSubcategory");
const { textFilter } = require("../utils/controller_get_process");

// Get all income subcategories from database only
exports.getAllIncomeSubcategories = async (req, res) => {
  try {
    // Get region from query parameter
    const region = req.query.region;
    
    // Create filter object
    const filter = {};
    
    // Add region filter if provided
    if (region) {
      filter.region = region;
      console.log(`Filtering income subcategories by region: ${region}`);
    }
    
    // Get existing subcategories from database
    const dbSubcategories = await IncomeSubcategory.find(filter)
      .sort({ incomeSubcategoryName: 1 });

    console.log(`Returning ${dbSubcategories.length} income subcategories from database for region: ${region || 'All'}`);

    return res.status(200).json({
      incomeSubcategories: dbSubcategories,
      fromDatabase: dbSubcategories.length,
      total: dbSubcategories.length,
      success: true
    });
  } catch (error) {
    console.error("Error in getAllIncomeSubcategories:", error);
    return res.status(500).json({
      error: "Failed to fetch income subcategories",
      success: false
    });
  }
};

// Create a new income subcategory
exports.addIncomeSubcategory = async (req, res) => {
  try {
    console.log("Request body:", req.body);
    const { incomeSubcategoryName } = req.body;
    
    console.log("Extracted field:", incomeSubcategoryName);

    if (!incomeSubcategoryName) {
      console.log("Validation failed: Income subcategory name is required");
      return res.status(400).json({ error: "Income subcategory name is required." });
    }

    // Check if a subcategory with this name already exists
    const existingSubcategory = await IncomeSubcategory.findOne({
      $or: [
        { incomeSubcategoryName: incomeSubcategoryName },
        { name: incomeSubcategoryName }
      ]
    });

    if (existingSubcategory) {
      return res.status(400).json({ error: "A subcategory with this name already exists." });
    }

    const newIncomeSubcategory = new IncomeSubcategory({
      incomeSubcategoryName,
      name: incomeSubcategoryName // Set both fields to ensure consistency
    });

    console.log("New subcategory object:", newIncomeSubcategory);
    await newIncomeSubcategory.save();
    
    console.log("Subcategory saved successfully");
    return res.status(201).json({
      message: "Income subcategory created successfully.",
      incomeSubcategory: newIncomeSubcategory,
    });
  } catch (error) {
    console.error("Error in addIncomeSubcategory:", error);
    return res.status(500).json({ error: "Failed to create income subcategory." });
  }
};

// Update an existing income subcategory
exports.editIncomeSubcategory = async (req, res) => {
  try {
    const { id } = req.params;
    const { incomeSubcategoryName } = req.body;

    if (!incomeSubcategoryName) {
      return res.status(400).json({ error: "Income subcategory name is required." });
    }

    // Check if another subcategory with this name already exists
    const existingSubcategory = await IncomeSubcategory.findOne({
      $or: [
        { incomeSubcategoryName: incomeSubcategoryName },
        { name: incomeSubcategoryName }
      ],
      _id: { $ne: id } // Exclude the current subcategory
    });

    if (existingSubcategory) {
      return res.status(400).json({ error: "Another subcategory with this name already exists." });
    }

    const updatedIncomeSubcategory = await IncomeSubcategory.findByIdAndUpdate(
      id,
      { 
        incomeSubcategoryName,
        name: incomeSubcategoryName // Update both fields
      },
      { new: true, runValidators: true }
    );

    if (!updatedIncomeSubcategory) {
      return res.status(404).json({ error: "Income subcategory not found." });
    }

    return res.json({
      message: "Income subcategory updated successfully.",
      incomeSubcategory: updatedIncomeSubcategory,
    });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to update income subcategory." });
  }
};

// Delete an income subcategory
exports.deleteIncomeSubcategory = async (req, res) => {
  try {
    const { id } = req.params;

    const deletedIncomeSubcategory = await IncomeSubcategory.findByIdAndDelete(id);
    if (!deletedIncomeSubcategory) {
      return res.status(404).json({ error: "Income subcategory not found." });
    }

    return res.json({ message: "Income subcategory deleted successfully." });
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: "Failed to delete income subcategory." });
  }
};

// Seed default income subcategories
exports.seedDefaultIncomeSubcategories = async (req, res) => {
  try {
    console.log("Starting to seed default income subcategories...");

    // Comprehensive list of income subcategories for government/organization use
    const defaultIncomeSubcategories = [
      // Tax Revenue
      "Real Property Tax",
      "Business Tax",
      "Community Tax",
      "Amusement Tax",
      "Franchise Tax",
      "Professional Tax",
      "Transfer Tax",
      "Idle Land Tax",

      // Service Income
      "Regulatory Fees",
      "Licensing Fees",
      "Permit Fees",
      "Certification Fees",
      "Registration Fees",
      "Processing Fees",
      "Application Fees",
      "Inspection Fees",

      // Charges for Services
      "Market Fees",
      "Slaughterhouse Fees",
      "Cemetery Fees",
      "Terminal Fees",
      "Parking Fees",
      "Garbage Collection Fees",
      "Water Service Fees",
      "Sewerage Service Fees",

      // Fines and Penalties
      "Traffic Violation Fines",
      "Building Code Violation Fines",
      "Environmental Violation Fines",
      "Business Permit Penalties",
      "Tax Penalties",
      "Late Payment Penalties",

      // Grants and Donations
      "National Government Grants",
      "Provincial Government Grants",
      "Foreign Grants",
      "Private Donations",
      "NGO Donations",
      "Corporate Donations",
      "Individual Donations",

      // Interest Income
      "Bank Interest",
      "Investment Interest",
      "Loan Interest",
      "Time Deposit Interest",
      "Government Securities Interest",

      // Rental Income
      "Building Rental",
      "Equipment Rental",
      "Vehicle Rental",
      "Land Rental",
      "Facility Rental",

      // Sale of Assets
      "Sale of Equipment",
      "Sale of Vehicles",
      "Sale of Real Property",
      "Sale of Supplies",
      "Sale of Scrap Materials",

      // Other Income
      "Miscellaneous Income",
      "Insurance Claims",
      "Refunds and Reimbursements",
      "Dividend Income",
      "Royalty Income",
      "Commission Income",
      "Subsidy Income",
      "Special Assessment",

      // Internal Revenue Allotment (IRA)
      "IRA - Current Year",
      "IRA - Prior Year Adjustment",
      "IRA - Special Shares",

      // Share from National Taxes
      "Share from Tobacco Tax",
      "Share from Excise Tax",
      "Share from VAT",
      "Share from Income Tax",

      // Special Purpose Funds
      "Special Education Fund",
      "Calamity Fund",
      "Development Fund",
      "Trust Fund Income"
    ];

    let createdCount = 0;
    let skippedCount = 0;
    const errors = [];

    for (const subcategoryName of defaultIncomeSubcategories) {
      try {
        // Check if subcategory already exists
        const existingSubcategory = await IncomeSubcategory.findOne({
          $or: [
            { incomeSubcategoryName: subcategoryName },
            { name: subcategoryName }
          ]
        });

        if (!existingSubcategory) {
          // Create new subcategory
          const newSubcategory = new IncomeSubcategory({
            incomeSubcategoryName: subcategoryName,
            name: subcategoryName
          });

          await newSubcategory.save();
          createdCount++;
          console.log(`✅ Created: ${subcategoryName}`);
        } else {
          skippedCount++;
          console.log(`⚠️ Exists: ${subcategoryName}`);
        }
      } catch (error) {
        errors.push(`${subcategoryName}: ${error.message}`);
        console.log(`❌ Error creating ${subcategoryName}: ${error.message}`);
      }
    }

    console.log(`Seeding completed: ${createdCount} created, ${skippedCount} skipped, ${errors.length} errors`);

    return res.status(200).json({
      message: "Default income subcategories seeding completed",
      created: createdCount,
      skipped: skippedCount,
      errors: errors.length > 0 ? errors : undefined,
      total: defaultIncomeSubcategories.length
    });
  } catch (error) {
    console.error("Error in seedDefaultIncomeSubcategories:", error);
    return res.status(500).json({
      error: "Failed to seed default income subcategories",
      details: error.message
    });
  }
};
