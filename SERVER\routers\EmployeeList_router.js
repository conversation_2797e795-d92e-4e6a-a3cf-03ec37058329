const {
  getAllEmployees,
  addEmployee,
  editEmployee,
  deleteEmployee,
  getEligibleForLoyaltyPay,
  getEligibleForRetirement,
  getEmployeeStats,
} = require("../controllers/EmployeeListController");

const Router = require("express").Router;

const employeeRouter = Router();

// Get all employees
employeeRouter.get("/employees", getAllEmployees);

// Add a new employee
employeeRouter.post("/employees", addEmployee);

// Edit an existing employee
employeeRouter.put("/employees/:id", editEmployee);

// Delete an employee
employeeRouter.delete("/employees/:id", deleteEmployee);

// Get employees eligible for loyalty pay
employeeRouter.get("/employees/loyalty-pay", getEligibleForLoyaltyPay);

// Get employees eligible for retirement
employeeRouter.get("/eligible-for-retirement", getEligibleForRetirement);

// Get employee statistics
employeeRouter.get("/employees/stats", getEmployeeStats);

module.exports = employeeRouter;
  
