const {
  bulkAddCOSPersonnel,
  getAllCOSPersonnel,
  updateCOSPersonnel,
  deleteCOSPersonnel,
  getGrandTotalCOS
} = require("../controllers/COSPersonnelController");

const Router = require("express").Router;
const checkDueDate = require("../middleware/checkDueDate");
const checkToken = require("../middleware/checkToken");

const cosPersonnelRouter = Router();

cosPersonnelRouter.get("/cosPersonnels", checkToken, getAllCOSPersonnel);
cosPersonnelRouter.post("/cos-personnel/bulk-add", checkToken, checkDueDate, bulkAddCOSPersonnel);
cosPersonnelRouter.put("/cos-personnel/:id", checkToken, checkDueDate, updateCOSPersonnel);
cosPersonnelRouter.delete("/cos-personnel/:id", checkToken, checkDueDate, deleteCOSPersonnel);
cosPersonnelRouter.get("/grandtotalCOS", checkToken, getGrandTotalCOS);

module.exports = cosPersonnelRouter;
