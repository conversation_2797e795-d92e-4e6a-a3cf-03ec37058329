const mongoose = require('mongoose');

const UserOrganizationalAssignmentSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    unique: true
  },
  userName: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  // Access scope defines the level of organizational access
  accessScope: {
    type: String,
    enum: ['FULL', 'REGION', 'DEPARTMENT', 'DIVISION', 'OWN_ONLY'],
    default: 'REGION'
  },
  // Region assignments (existing functionality)
  regions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'region'
  }],
  // Department assignments
  departments: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'department'
  }],
  // Division assignments (for future use)
  divisions: [{
    type: String // Store as string for now, can be ObjectId later
  }],
  // Role-based permissions
  rolePermissions: {
    canCreateProposals: { type: Boolean, default: true },
    canEditOwnProposals: { type: Boolean, default: true },
    canEditAllProposals: { type: Boolean, default: false },
    canDeleteProposals: { type: Boolean, default: false },
    canApproveProposals: { type: Boolean, default: false },
    canViewReports: { type: Boolean, default: true },
    canManageUsers: { type: Boolean, default: false },
    canManageSettings: { type: Boolean, default: false }
  },
  // Operational restrictions
  restrictions: {
    maxProposalAmount: { type: Number, default: null }, // null = no limit
    allowedBudgetTypes: [{ type: String }], // ['Initial', 'NEP', 'GAA']
    allowedFiscalYears: [{ type: String }],
    readOnlyMode: { type: Boolean, default: false }
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: String
  },
  lastModifiedBy: {
    type: String
  }
});

// Pre-save middleware to update timestamps
UserOrganizationalAssignmentSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Instance methods
UserOrganizationalAssignmentSchema.methods.hasRegionAccess = function(regionId) {
  if (this.accessScope === 'FULL') return true;
  return this.regions.some(r => r.toString() === regionId.toString());
};

UserOrganizationalAssignmentSchema.methods.hasDepartmentAccess = function(departmentId) {
  if (this.accessScope === 'FULL') return true;
  if (this.accessScope === 'OWN_ONLY') return false;
  return this.departments.some(d => d.toString() === departmentId.toString());
};

UserOrganizationalAssignmentSchema.methods.hasDivisionAccess = function(divisionName) {
  if (this.accessScope === 'FULL') return true;
  if (this.accessScope === 'OWN_ONLY') return false;
  return this.divisions.includes(divisionName);
};

// Static methods
UserOrganizationalAssignmentSchema.statics.findByUserId = function(userId) {
  return this.findOne({ userId }).populate('regions departments');
};

UserOrganizationalAssignmentSchema.statics.getUserPermissions = async function(userId) {
  const assignment = await this.findByUserId(userId);
  if (!assignment) {
    // Default permissions for users without assignments
    return {
      accessScope: 'OWN_ONLY',
      regions: [],
      departments: [],
      divisions: [],
      rolePermissions: {
        canCreateProposals: true,
        canEditOwnProposals: true,
        canEditAllProposals: false,
        canDeleteProposals: false,
        canApproveProposals: false,
        canViewReports: false,
        canManageUsers: false,
        canManageSettings: false
      }
    };
  }
  return assignment;
};

module.exports = mongoose.model('UserOrganizationalAssignment', UserOrganizationalAssignmentSchema);
