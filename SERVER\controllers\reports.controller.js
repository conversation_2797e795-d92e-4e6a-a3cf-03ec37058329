const Settings = require("../models/Settings");
const PersonnelServices = require("../models/personnelServices");
const MOOEProposal = require("../models/mooeProposals");
const CapitalOutlay = require("../models/CapitalOutlay");
const Category = require("../models/Category");
const Income = require("../models/Income");
const Region = require("../models/Region");
const IncomeCategory = require("../models/IncomeCategory");
const IncomeSubCategory = require("../models/IncomeSubcategory");
const IAsOMCost = require("../models/IAsOMCost");

const getConsolidatedSummary = async (req, res) => {
  try {
    let fiscalYear;
    // Determine fiscal year
    if (req.query.fiscalYear) {
      fiscalYear = parseInt(req.query.fiscalYear, 10);
    } else {
      const activeSetting = await Settings.findOne({ isActive: true });
      if (!activeSetting)
        return res.status(404).json({ error: "No active settings found." });
      fiscalYear = activeSetting.fiscalYear;
    }

    // Build filter from query params
    const { region, department } = req.query;
    const filter = { fiscalYear };
    if (region && region.trim()) filter.region = region.trim();
    if (department && department.trim()) filter.department = department.trim();

    // Fetch data
    const psEntries = await PersonnelServices.find(filter);
    const mooeEntries = await MOOEProposal.find(filter)
      .populate({ path: "uacsCode", select: "uacsCode lineItem subLineItem accountingTitle" });
    
    // Properly populate the category field
    const coEntries = await CapitalOutlay.find(filter)
      .populate({ 
        path: "category", 
        select: "categoryName _id",
        model: Category
      });
      
    // Fetch IAs O&M Cost data
    let iasOMCostData = await IAsOMCost.findOne(filter);
    if (!iasOMCostData) {
      // Create default empty data if none exists
      iasOMCostData = {
        nis: 0,
        cis: 0,
        nisSubsidy: 0,
        cisSubsidy: 0
      };
    }

    // Sum Personnel Services fields (excluding annualSalary)
    const psFieldsToSum = [
      "PERA", "RATA", "honoraria", "uniformALLOWANCE",
      "subsistenceAllowance", "subsistenceAllowanceMDS", "subsistenceAllowanceST",
      "productivityIncentive", "medical", "childrenAllowance", "meal",
      "cashGift", "midyearBonus", "yearEndBonus", "retirementBenefits",
      "terminalLeave", "earnedLeaves", "gsisPremium", "pagibigPremium",
      "philhealthPremium", "employeeCompensation", "overtimePay", "hazardPay",
      "courtAppearance", "loyaltyAward"
    ];
    const psSummary = {};
    psFieldsToSum.forEach(field => {
      psSummary[field] = psEntries.reduce(
        (sum, e) => sum + (Number(e[field]) || 0),
        0
      );
    });

    // Compute annualSalary breakdown by appointment type
    const permAnnual = psEntries
      .filter(e => e.statusOfAppointment.toUpperCase() === "PERMANENT")
      .reduce((sum, e) => sum + (Number(e.annualSalary) || 0), 0);
    const casualAnnual = psEntries
      .filter(e => e.statusOfAppointment.toUpperCase() === "CASUAL")
      .reduce((sum, e) => sum + (Number(e.annualSalary) || 0), 0);
    psSummary.annualSalaryPermanent = permAnnual;
    psSummary.annualSalaryCasual    = casualAnnual;    // Map MOOE entries
    const mooe = mooeEntries.map(entry => {
      // Calculate total amount including income, subsidy, and IAs O&M Cost
      const totalAmount = (Number(entry.income) || 0) + 
                         (Number(entry.subsidy) || 0) + 
                         (entry.uacsCode === "5-02-99-990-NIS" ? (Number(entry.nis) || 0) : 0) +
                         (entry.uacsCode === "5-02-99-990-CIS" ? (Number(entry.cis) || 0) : 0);

      return {
        uacsCode: entry.uacsCode?.uacsCode || entry.uacsCode,
        lineItem: entry.uacsCode?.lineItem,
        subLineItem: entry.sublineItem || entry.uacsCode?.subLineItem,
        accountingTitle: entry.accountingTitle || entry.uacsCode?.accountingTitle,
        amount: totalAmount,
        region: entry.region || '',
        department: entry.department || '',
        income: Number(entry.income) || 0,
        subsidy: Number(entry.subsidy) || 0
      };
    });

    // Map Capital Outlay entries with proper category handling
    const co = await Promise.all(coEntries.map(async (entry) => {
      let categoryName = 'Uncategorized';
      
      // If category is populated, use its categoryName
      if (entry.category && typeof entry.category === 'object' && entry.category.categoryName) {
        categoryName = entry.category.categoryName;
      } 
      // If category is an ObjectId, fetch the category
      else if (entry.category && typeof entry.category !== 'object') {
        try {
          const categoryDoc = await Category.findById(entry.category);
          if (categoryDoc) {
            categoryName = categoryDoc.categoryName;
          }
        } catch (err) {
          console.error(`Error fetching category for ID ${entry.category}:`, err);
        }
      }
      
      return {
        category: categoryName,
        particulars: entry.particulars || '',
        subLineItem: entry.sublineItem || '',
        accountingTitle: entry.accountingTitle || '',
        uacsCode: entry.uacsCode || '',
        cost: entry.cost || 0,
        region: entry.region || '',
        department: entry.department || ''
      };
    }));    // Calculate MOOE Regular total
    const mooeRegularTotal = mooe.reduce((sum, e) => sum + (Number(e.amount) || 0), 0);
    
    // Calculate IAs O&M Cost total
    const iasOMTotal = (Number(iasOMCostData?.nis || 0) + 
                       Number(iasOMCostData?.cis || 0) +
                       Number(iasOMCostData?.nisSubsidy || 0) + 
                       Number(iasOMCostData?.cisSubsidy || 0));

    // Calculate combined MOOE total (Regular + IAs O&M)
    const mooeTotal = mooeRegularTotal;

    // Calculate Capital Outlay total
    const coTotal = co.reduce((sum, e) => sum + (Number(e.cost) || 0), 0);

    // Add IAs O&M Cost to psSummary for the PDF report
    psSummary.iasOMCost = iasOMCostData;
    
    // Return consolidated summary with detailed MOOE breakdown
    res.json({ 
      fiscalYear, 
      psSummary, 
      mooe, 
      mooeTotal,
      mooeBreakdown: {
        regular: mooeRegularTotal,
        iasOM: iasOMTotal,
        total: mooeRegularTotal + iasOMTotal
      }, 
      co, 
      coTotal 
    });
  } catch (err) {
    console.error("Error in getConsolidatedSummary:", err);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

/**
 * Get consolidated corporate income report by region
 */
const getConsolidatedCorporateIncome = async (req, res) => {
  try {
    console.log("Received request for consolidated corporate income with params:", req.query);
    
    // Determine fiscal year
    let fiscalYear;
    if (req.query.fiscalYear) {
      fiscalYear = req.query.fiscalYear;
    } else {
      const activeSetting = await Settings.findOne({ isActive: true });
      if (!activeSetting)
        return res.status(404).json({ error: "No active settings found." });
      fiscalYear = activeSetting.fiscalYear;
    }
    console.log(`Using fiscal year: ${fiscalYear}`);

    // Get budget type from query or default to "INITIAL"
    const budgetType = (req.query.budgetType || "INITIAL").toUpperCase();
    console.log(`Using budget type: ${budgetType}`);

    // Get all regions
    const regions = await Region.find().sort({ Region: 1 });
    console.log(`Found ${regions.length} regions`);
    
    // Get all income categories
    const incomeCategories = await IncomeCategory.find().sort({ incomeCategoryName: 1 });
    console.log(`Found ${incomeCategories.length} income categories`);
    
    // Get all income subcategories
    const incomeSubCategories = await IncomeSubCategory.find().sort({ incomeSubcategoryName: 1 });
    console.log(`Found ${incomeSubCategories.length} income subcategories`);
    
    // Fetch all income entries for the fiscal year and budget type
    // Create a more flexible query for budget type
    const budgetTypeQuery = { $regex: new RegExp(budgetType, 'i') };
    
    // Build the query object
    const query = { fiscalYear };
    
    // Only add budget type filter if it's specified
    if (budgetType) {
      query.budgetType = budgetTypeQuery;
    }
    
    console.log("Income query:", JSON.stringify(query));
    
    const incomeEntries = await Income.find(query).populate('incomecategory');
    
    console.log(`Found ${incomeEntries.length} income entries for FY ${fiscalYear} and budget type ${budgetType}`);

    // Initialize result structure with hierarchical data
    const result = {
      fiscalYear,
      budgetType,
      regions: regions.map(r => r.Region),
      hierarchicalData: [],
      regionData: {},
      categoryTotals: {},
      subcategoryTotals: {},
      categorySubcategoryMap: {}, // Map to track which subcategories belong to which category
      grandTotal: 0
    };

    // Initialize region data structure
    regions.forEach(region => {
      result.regionData[region.Region] = {
        categoryAmounts: {},
        subcategoryAmounts: {},
        total: 0
      };
      
      // Initialize category amounts for each region
      incomeCategories.forEach(category => {
        result.regionData[region.Region].categoryAmounts[category._id.toString()] = 0;
      });
      
      // Initialize subcategory amounts for each region
      incomeSubCategories.forEach(subcategory => {
        result.regionData[region.Region].subcategoryAmounts[subcategory._id.toString()] = 0;
      });
    });

    // Initialize category totals and prepare hierarchical structure
    incomeCategories.forEach(category => {
      const categoryId = category._id.toString();
      result.categoryTotals[categoryId] = 0;
      
      // Add category to hierarchical data
      result.hierarchicalData.push({
        id: categoryId,
        name: category.incomeCategoryName,
        type: 'category',
        subcategories: [],
        order: category.order || 0 // Use order if available
      });
      
      // Initialize the subcategory map for this category
      result.categorySubcategoryMap[categoryId] = [];
    });
    
    // Initialize subcategory totals
    incomeSubCategories.forEach(subcategory => {
      const subcategoryId = subcategory._id.toString();
      result.subcategoryTotals[subcategoryId] = 0;
    });

    // First pass: Process income entries and collect subcategory information
    const subcategoryByCategoryMap = new Map(); // Map to track which subcategories belong to which category
    
    incomeEntries.forEach(entry => {
      const region = entry.region || 'Unspecified';
      const categoryId = entry.incomecategory?._id?.toString() || 'Uncategorized';
      const subcategoryName = entry.subcategory || 'Uncategorized';
      const amount = Number(entry.amount) || 0;
      
      // Skip if region doesn't exist in our structure
      if (!result.regionData[region]) {
        console.log(`Skipping entry with unknown region: ${region}`);
        return;
      }
      
      // Skip if category doesn't exist in our structure
      if (result.categoryTotals[categoryId] === undefined) {
        console.log(`Skipping entry with unknown category ID: ${categoryId}`);
        return;
      }
      
      // Add to region's category amount
      result.regionData[region].categoryAmounts[categoryId] += amount;
      
      // Track subcategory to category relationship
      if (subcategoryName && subcategoryName !== 'Uncategorized') {
        if (!subcategoryByCategoryMap.has(categoryId)) {
          subcategoryByCategoryMap.set(categoryId, new Set());
        }
        subcategoryByCategoryMap.get(categoryId).add(subcategoryName);
      }
      
      // Find matching subcategory ID
      const matchingSubcategory = incomeSubCategories.find(
        sc => (sc.incomeSubcategoryName || sc.name).toLowerCase() === subcategoryName.toLowerCase()
      );
      
      // If we found a matching subcategory, update its amounts
      if (matchingSubcategory) {
        const subcategoryId = matchingSubcategory._id.toString();
        
        // Add to region's subcategory amount
        if (result.regionData[region].subcategoryAmounts[subcategoryId] !== undefined) {
          result.regionData[region].subcategoryAmounts[subcategoryId] += amount;
        }
        
        // Update subcategory total
        if (result.subcategoryTotals[subcategoryId] !== undefined) {
          result.subcategoryTotals[subcategoryId] += amount;
        }
        
        // Add to category-subcategory map if not already there
        if (!result.categorySubcategoryMap[categoryId].includes(subcategoryId)) {
          result.categorySubcategoryMap[categoryId].push(subcategoryId);
        }
      }
      
      // Update region total
      result.regionData[region].total += amount;
      
      // Update category total
      result.categoryTotals[categoryId] += amount;
      
      // Update grand total
      result.grandTotal += amount;
    });
    
    // Second pass: Build the hierarchical structure
    // For each category, add its subcategories to the hierarchical data
    result.hierarchicalData.forEach(categoryItem => {
      const categoryId = categoryItem.id;
      const subcategoryIds = result.categorySubcategoryMap[categoryId] || [];
      
      // Add subcategories to this category
      subcategoryIds.forEach(subcategoryId => {
        const subcategory = incomeSubCategories.find(sc => sc._id.toString() === subcategoryId);
        if (subcategory) {
          categoryItem.subcategories.push({
            id: subcategoryId,
            name: subcategory.incomeSubcategoryName || subcategory.name,
            type: 'subcategory',
            parentId: categoryId,
            order: subcategory.order || 0 // Use order if available
          });
        }
      });
      
      // Sort subcategories by name
      categoryItem.subcategories.sort((a, b) => a.name.localeCompare(b.name));
    });
    
    // Sort categories by name
    result.hierarchicalData.sort((a, b) => a.name.localeCompare(b.name));

    // If there's no data, still return the structure but with a message
    if (incomeEntries.length === 0) {
      result.message = "No income data found for the selected fiscal year and budget type.";
    }

    console.log(`Returning result with grand total: ${result.grandTotal}`);
    res.json(result);
  } catch (err) {
    console.error("Error in getConsolidatedCorporateIncome:", err);
    res.status(500).json({ error: "Internal Server Error", details: err.message });
  }
};

module.exports = { 
  getConsolidatedSummary,
  getConsolidatedCorporateIncome
};
