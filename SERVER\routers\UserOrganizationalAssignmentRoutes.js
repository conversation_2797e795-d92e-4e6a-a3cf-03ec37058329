const {
  getAllUserOrganizationalAssignments,
  getUserOrganizationalAssignments,
  getCurrentUserOrganizationalAssignments,
  createOrUpdateUserOrganizationalAssignment,
  deleteUserOrganizationalAssignment,
  getUserRolePermissions,
  bulkUpdateRolePermissions
} = require('../controllers/UserOrganizationalAssignmentController');

const Router = require('express').Router;
const { 
  authenticatedRoute, 
  adminRoute, 
  PERMISSION_LEVELS 
} = require('../middleware/securityMiddleware');

const userOrgAssignmentRouter = Router();

// 🔒 SECURED ROUTES

// Get all user organizational assignments (Admin only)
userOrgAssignmentRouter.get(
  '/user-organizational-assignments', 
  ...adminRoute(), 
  getAllUserOrganizationalAssignments
);

// Get assignments for a specific user (Admin only)
userOrgAssignmentRouter.get(
  '/user-organizational-assignments/:userId', 
  ...adminRoute(), 
  getUserOrganizationalAssignments
);

// Get current user's organizational assignments (Authenticated users)
userOrgAssignmentRouter.get(
  '/my-organizational-assignments', 
  ...authenticatedRoute(), 
  getCurrentUserOrganizationalAssignments
);

// Create or update user organizational assignment (Admin only)
userOrgAssignmentRouter.post(
  '/user-organizational-assignments', 
  ...adminRoute(), 
  createOrUpdateUserOrganizationalAssignment
);

// Update user organizational assignment (Admin only)
userOrgAssignmentRouter.put(
  '/user-organizational-assignments/:userId', 
  ...adminRoute(), 
  createOrUpdateUserOrganizationalAssignment
);

// Delete user organizational assignment (Admin only)
userOrgAssignmentRouter.delete(
  '/user-organizational-assignments/:userId', 
  ...adminRoute(), 
  deleteUserOrganizationalAssignment
);

// Get current user's role permissions (Authenticated users)
userOrgAssignmentRouter.get(
  '/my-role-permissions', 
  ...authenticatedRoute(), 
  getUserRolePermissions
);

// Bulk update role permissions (Admin only)
userOrgAssignmentRouter.post(
  '/bulk-update-role-permissions', 
  ...adminRoute(), 
  bulkUpdateRolePermissions
);

module.exports = userOrgAssignmentRouter;
