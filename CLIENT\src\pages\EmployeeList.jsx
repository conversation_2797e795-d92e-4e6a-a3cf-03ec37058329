import React, { useState, useEffect } from "react";
import { format } from "date-fns";
import {
  Switch,
  Box,
  Typography,
  Chip,
  Avatar,
  Tooltip,
  Badge,
  Card,
  CardContent,
  Grid,
  Divider,
  useTheme,
  useMediaQuery,
  Paper
} from "@mui/material";
import EmployeeCustomPage from "../components/employee/EmployeeCustomPage";
import EmployeeCustomAdd from "../components/employee/EmployeeCustomAdd";
import TextSearchable from "../global/components/TextSearchable";
import api from "../config/api";
import { toast } from "react-hot-toast";
import PersonIcon from '@mui/icons-material/Person';
import WorkIcon from '@mui/icons-material/Work';
import EventIcon from '@mui/icons-material/Event';
import { useThemeContext } from "../context/ThemeContext";
import { useRegion } from "../context/RegionContext";


const EmployeeList = () => {
  const theme = useTheme();
  const { mode } = useThemeContext();
  const { activeRegion } = useRegion();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [employeeStats, setEmployeeStats] = useState({
    total: 0,
    active: 0,
    inactive: 0
  });
  
  // State to trigger refreshes
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch employee statistics based on active region
  useEffect(() => {
    const fetchEmployeeStats = async () => {
      if (!activeRegion) {
        console.log("No active region selected, skipping stats fetch");
        setEmployeeStats({
          total: 0,
          active: 0,
          inactive: 0
        });
        return;
      }
      
      try {
        // Get the region name - ensure we're using the exact region name
        const regionName = activeRegion.name || activeRegion.regionName;
        
        if (!regionName) {
          console.error("Invalid region name");
          toast.error("Invalid region selected");
          return;
        }
        
        console.log(`Fetching employee stats for region: ${regionName}`);
        
        // Include the active region in the request
        // Use the correct endpoint from EmployeeList_router.js
        const response = await api.get('/employees/stats', { 
          params: { region: regionName } 
        });
        
        console.log("Employee stats response:", response.data);
        
        if (response.data) {
          setEmployeeStats(response.data);
        }
      } catch (error) {
        console.error("Error fetching employee statistics:", error);
        console.error("Error details:", error.response?.data || error.message);
        
        // Set default values in case of error
        setEmployeeStats({
          total: 0,
          active: 0,
          inactive: 0
        });
        
        // Show error toast
        toast.error(error.response?.data?.message || "Failed to load employee statistics", {
          style: {
            borderRadius: '10px',
            background: theme.palette.mode === 'dark' ? '#333' : '#fff',
            color: theme.palette.mode === 'dark' ? '#fff' : '#333',
          },
        });
      }
    };
    
    fetchEmployeeStats();
  }, [activeRegion, theme.palette.mode, refreshTrigger]); // Re-fetch when active region changes or refresh is triggered
  
  // Refresh stats when employee data changes
  const refreshStats = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const employeeSchema = {
    action: {
      type: "action",
      label: "Actions",
    },
    EmployeeFullName: {
      type: "text",
      label: "Employee Name",
      customRender: (row) => (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Avatar 
            sx={{ 
              bgcolor: theme.palette.primary.main,
              width: 32,
              height: 32,
              fontSize: '0.9rem'
            }}
          >
            {row.EmployeeFullName ? row.EmployeeFullName.charAt(0) : 'E'}
          </Avatar>
          <TextSearchable 
            columnName={row.EmployeeFullName} 
            sx={{ 
              fontWeight: 500,
              '&:hover': {
                color: theme.palette.primary.main,
                textDecoration: 'underline'
              }
            }}
          />
        </Box>
      ),
      show: true,
      searchable: true,
    },
    Department: {
      type: "text",
      label: "Department",
      customRender: (row) => (
        <Tooltip title={`Department: ${row.Department}`} arrow>
          <Box>
            <TextSearchable 
              columnName={row.Department} 
              sx={{ 
                maxWidth: '150px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                display: 'block'
              }}
            />
          </Box>
        </Tooltip>
      ),
      show: true,
      searchable: true,
    },
    Division: {
      type: "text",
      label: "Division",
      searchable: true,
      show: true,
      customRender: (row) => (
        <Tooltip title={`Division: ${row.Division || 'N/A'}`} arrow>
          <Typography 
            sx={{ 
              maxWidth: '150px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            {row.Division || 'N/A'}
          </Typography>
        </Tooltip>
      ),
    },
    Section: {
      type: "text",
      label: "Section",
      searchable: true,
      show: true,
      customRender: (row) => (
        <Typography 
          sx={{ 
            maxWidth: '150px',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}
        >
          {row.Section || 'N/A'}
        </Typography>
      ),
    },
    Region: {
      type: "text",
      label: "Region",
      searchable: true,
      show: true,
      customRender: (row) => (
        <Chip 
          label={row.Region} 
          size="small" 
          sx={{ 
            bgcolor: theme.palette.mode === 'light' ? '#e8f5e9' : '#1b5e20',
            color: theme.palette.mode === 'light' ? '#1b5e20' : '#ffffff',
            fontWeight: 500
          }}
        />
      ),
    },
    PositionTitle: {
      type: "text",
      label: "Position Title",
      searchable: true,
      show: true,
      customRender: (row) => (
        <Tooltip title={row.PositionTitle} arrow>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <WorkIcon fontSize="small" color="action" />
            <Typography 
              sx={{ 
                maxWidth: '150px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {row.PositionTitle}
            </Typography>
          </Box>
        </Tooltip>
      ),
    },
    StatusOfAppointment: {
      type: "text",
      label: "Appointment",
      searchable: true,
      show: true,
      customRender: (row) => (
        <Chip 
          label={row.StatusOfAppointment} 
          size="small"
          sx={{ 
            bgcolor: theme.palette.mode === 'light' ? '#e3f2fd' : '#0d47a1',
            color: theme.palette.mode === 'light' ? '#0d47a1' : '#ffffff'
          }}
        />
      ),
    },
    SalaryInfo: {
      type: "custom",
      label: "Salary Info",
      show: true,
      customRender: (row) => (
        <Tooltip 
          title={
            <Box>
              <Typography variant="caption">SG: {row.SG || 'N/A'}</Typography><br />
              <Typography variant="caption">Step: {row.Step || 'N/A'}</Typography><br />
              <Typography variant="caption">JG: {row.JG || 'N/A'}</Typography><br />
              <Typography variant="caption">Rate: ₱{row.Rate ? row.Rate.toLocaleString() : 'N/A'}</Typography>
            </Box>
          } 
          arrow
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Typography variant="body2" fontWeight={500}>
              SG: {row.SG || 'N/A'}
            </Typography>
            <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
            <Typography variant="body2">
              ₱{row.Rate ? row.Rate.toLocaleString() : 'N/A'}
            </Typography>
          </Box>
        </Tooltip>
      ),
    },
    DateOfAppointment: {
      type: "date",
      label: "Appointment Date",
      searchable: true,
      show: true,
      customRender: (row) => {
        if (!row.DateOfAppointment) return 'N/A';
        
        const date = new Date(row.DateOfAppointment);
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <EventIcon fontSize="small" color="action" />
            <Typography variant="body2">
              {format(date, "MMM dd, yyyy")}
            </Typography>
          </Box>
        );
      },
    },
    employeeStatus: {
      type: "text",
      label: "Status",
      searchable: true,
      show: true,
      customRender: (row) => {
        const handleStatusToggle = async (checked) => {
          const newStatus = checked ? "Active" : "Inactive";
          try {
            await api.put(`/employees/${row._id}`, { employeeStatus: newStatus });
            toast.success(`Employee status updated to ${newStatus}`, {
              style: {
                borderRadius: '10px',
                background: theme.palette.mode === 'dark' ? '#333' : '#fff',
                color: theme.palette.mode === 'dark' ? '#fff' : '#333',
              },
              iconTheme: {
                primary: newStatus === 'Active' ? '#4caf50' : '#f44336',
                secondary: '#FFFAEE',
              },
            });
            return true;
          } catch (error) {
            toast.error("Failed to update employee status", {
              style: {
                borderRadius: '10px',
                background: theme.palette.mode === 'dark' ? '#333' : '#fff',
                color: theme.palette.mode === 'dark' ? '#fff' : '#333',
              },
            });
            console.error("Error updating status:", error);
            return false;
          }
        };

        return {
          render: (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip 
                label={row.employeeStatus || "N/A"} 
                color={row.employeeStatus === "Active" ? "success" : "error"}
                size="small"
                sx={{ minWidth: '70px' }}
              />
              <Switch
                checked={row.employeeStatus === "Active"}
                onChange={async (e) => {
                  const success = await handleStatusToggle(e.target.checked);
                  return success;
                }}
                color="primary"
                size="small"
                inputProps={{ "aria-label": "employee status toggle" }}
              />
            </Box>
          ),
          onChange: handleStatusToggle,
        };
      },
    },
  };

  return (
    <>
      {!activeRegion ? (
        <Paper 
          elevation={3} 
          sx={{ 
            p: 4, 
            textAlign: 'center',
            borderRadius: '12px',
            bgcolor: theme.palette.mode === 'light' ? '#fff' : theme.palette.background.paper,
          }}
        >
          <Typography variant="h5" color="error" gutterBottom>
            No Region Selected
          </Typography>
          <Typography variant="body1" paragraph>
            Please select a region from the region selector to view employee data.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Employee data is region-specific for security purposes.
          </Typography>
          {/* No additional content needed */}
        </Paper>
      ) : (
        <EmployeeCustomPage
        dataListName="employees"
        title="Employee Management"
        description={`View, add, edit and manage all employee records${activeRegion ? ` for ${activeRegion.name || activeRegion.regionName} region` : ''}`}
        schema={employeeSchema}
        searchable={true}
        hasAdd={true}
        hasEdit={true}
        hasDelete={false}
        ROWS_PER_PAGE={20}
        customAddElement={
          <EmployeeCustomAdd onDataChange={refreshStats} />
        }
        onDataChange={refreshStats}
        tableContainerProps={{ 
          sx: { 
            height: 'calc(100vh - 350px)',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            '& .MuiTableRow-root:hover': {
              backgroundColor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.04)' : 'rgba(255, 255, 255, 0.08)',
            }
          } 
        }}
        headerContent={
          <Box sx={{ mt: 3, mb: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Card 
                  elevation={2}
                  sx={{ 
                    bgcolor: theme.palette.mode === 'light' ? '#ffffff' : theme.palette.background.paper,
                    borderLeft: `4px solid ${theme.palette.primary.main}`,
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                    }
                  }}
                >
                  <CardContent>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Total Employees
                    </Typography>
                    <Typography variant="h3" component="div" color="primary" fontWeight="bold">
                      {employeeStats.total}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <PersonIcon color="primary" />
                      <Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
                        {activeRegion ? `All employees in ${activeRegion.name || activeRegion.regionName}` : 'All registered employees'}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Card 
                  elevation={2}
                  sx={{ 
                    bgcolor: theme.palette.mode === 'light' ? '#ffffff' : theme.palette.background.paper,
                    borderLeft: '4px solid #4caf50',
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                    }
                  }}
                >
                  <CardContent>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Active Employees
                    </Typography>
                    <Typography variant="h3" component="div" sx={{ color: '#4caf50' }} fontWeight="bold">
                      {employeeStats.active}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Badge color="success" variant="dot" sx={{ mr: 1 }}>
                        <PersonIcon sx={{ color: '#4caf50' }} />
                      </Badge>
                      <Typography variant="body2" color="textSecondary">
                        Currently active employees
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Card 
                  elevation={2}
                  sx={{ 
                    bgcolor: theme.palette.mode === 'light' ? '#ffffff' : theme.palette.background.paper,
                    borderLeft: '4px solid #f44336',
                    transition: 'transform 0.3s, box-shadow 0.3s',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 8px 16px rgba(0,0,0,0.1)'
                    }
                  }}
                >
                  <CardContent>
                    <Typography variant="h6" color="textSecondary" gutterBottom>
                      Inactive Employees
                    </Typography>
                    <Typography variant="h3" component="div" sx={{ color: '#f44336' }} fontWeight="bold">
                      {employeeStats.inactive}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                      <Badge color="error" variant="dot" sx={{ mr: 1 }}>
                        <PersonIcon sx={{ color: '#f44336' }} />
                      </Badge>
                      <Typography variant="body2" color="textSecondary">
                        Currently inactive employees
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        }
        footerElement={
          <Box 
            sx={{ 
              width: '100%',
              py: 1, 
              borderTop: `1px solid ${theme.palette.mode === 'light' ? '#e0e0e0' : '#424242'}`,
              textAlign: 'center',
              bgcolor: theme.palette.mode === 'light' ? '#f5f5f5' : '#333333',
              borderRadius: '0 0 8px 8px'
            }}
          >
            <Typography variant="caption" color="text.secondary">
              © {new Date().getFullYear()} NIA – ONLINE SUBMISSION OF BUDGET PROPOSAL INFORMATION SYSTEM
            </Typography>
          </Box>
        }
      />
      )}
    </>
  );
};

export default EmployeeList;
