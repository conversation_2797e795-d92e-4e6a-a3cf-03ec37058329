const mongoose = require('mongoose');

const UserRegionAssignmentSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
  },
  userName: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
  },
  regions: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'region'
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('userRegionAssignment', UserRegionAssignmentSchema);

