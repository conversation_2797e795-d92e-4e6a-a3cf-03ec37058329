/**
 * Test file to verify Chart of Accounts API functionality
 */

const testAPI = async () => {
  console.log("🧪 Testing Chart of Accounts API Functionality");
  console.log("=" .repeat(60));

  const baseURL = "http://localhost:5005";

  try {
    // Test 1: GET all chart of accounts
    console.log("\n📋 Test 1: GET /chart-of-accounts");
    const getResponse = await fetch(`${baseURL}/chart-of-accounts`);
    const getData = await getResponse.json();
    console.log("✅ GET Response:", JSON.stringify(getData, null, 2));

    // Test 2: POST new chart of account
    console.log("\n➕ Test 2: POST /chart-of-accounts");
    const newAccount = {
      accountClass: "Asset",
      lineItem: "Infrastructure Outlay",
      sublineItem: "Infrastructure Outlay",
      accountingTitle: "Road Construction and Improvement",
      uacsCode: "5-01-01-010"
    };

    const postResponse = await fetch(`${baseURL}/chart-of-accounts`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(newAccount)
    });

    if (postResponse.ok) {
      const postData = await postResponse.json();
      console.log("✅ POST Response:", JSON.stringify(postData, null, 2));
      
      // Test 3: GET all chart of accounts again to verify creation
      console.log("\n📋 Test 3: GET /chart-of-accounts (after creation)");
      const getResponse2 = await fetch(`${baseURL}/chart-of-accounts`);
      const getData2 = await getResponse2.json();
      console.log("✅ GET Response (after creation):", JSON.stringify(getData2, null, 2));
      
    } else {
      const errorData = await postResponse.text();
      console.log("❌ POST Error:", errorData);
    }

    // Test 4: Test another account
    console.log("\n➕ Test 4: POST another chart of account");
    const newAccount2 = {
      accountClass: "Asset",
      lineItem: "Building and Other Structures",
      sublineItem: "Building and Other Structures",
      accountingTitle: "School Buildings",
      uacsCode: "5-01-02-020"
    };

    const postResponse2 = await fetch(`${baseURL}/chart-of-accounts`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(newAccount2)
    });

    if (postResponse2.ok) {
      const postData2 = await postResponse2.json();
      console.log("✅ POST Response 2:", JSON.stringify(postData2, null, 2));
    } else {
      const errorData2 = await postResponse2.text();
      console.log("❌ POST Error 2:", errorData2);
    }

    // Test 5: Final GET to see all accounts
    console.log("\n📋 Test 5: Final GET /chart-of-accounts");
    const finalResponse = await fetch(`${baseURL}/chart-of-accounts`);
    const finalData = await finalResponse.json();
    console.log("✅ Final GET Response:", JSON.stringify(finalData, null, 2));

    console.log("\n" + "=" .repeat(60));
    console.log("🎉 Chart of Accounts API Test Completed!");
    console.log(`✅ Total accounts created: ${finalData.chartOfAccounts?.length || 0}`);
    console.log("=" .repeat(60));

  } catch (error) {
    console.error("❌ API Test Error:", error.message);
  }
};

// Run the test
testAPI();
